from fastapi import APIRouter

from app.api.v1.endpoints import experiments, students, analysis, models, upload, classes, grades, dashboard

api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(
    dashboard.router,
    prefix="/dashboard",
    tags=["dashboard"]
)

api_router.include_router(
    experiments.router,
    prefix="/experiments",
    tags=["experiments"]
)

api_router.include_router(
    students.router,
    prefix="/students",
    tags=["students"]
)

api_router.include_router(
    classes.router,
    prefix="/classes",
    tags=["classes"]
)

api_router.include_router(
    grades.router,
    prefix="/grades",
    tags=["grades"]
)

api_router.include_router(
    analysis.router,
    prefix="/analysis",
    tags=["analysis"]
)

api_router.include_router(
    models.router,
    prefix="/models",
    tags=["models"]
)

api_router.include_router(
    upload.router,
    prefix="/upload",
    tags=["upload"]
)
