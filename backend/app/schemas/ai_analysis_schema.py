from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class AnalysisType(str, Enum):
    """分析类型枚举"""
    AUTO = "auto"
    MANUAL = "manual"
    HYBRID = "hybrid"


class AnalysisStatus(str, Enum):
    """分析状态枚举"""
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AIAnalysisBase(BaseModel):
    """AI分析基础Schema"""
    student_id: str = Field(..., description="学生ID")
    experiment_type: str = Field(..., description="实验类型")
    analysis_type: AnalysisType = Field(AnalysisType.AUTO, description="分析类型")
    analysis_data: Dict[str, Any] = Field(..., description="详细分析数据")
    conclusions: Optional[List[str]] = Field(None, description="结论列表")
    suggestions: Optional[List[str]] = Field(None, description="改进建议")
    error_analysis: Optional[Dict[str, Any]] = Field(None, description="错误分析")
    scoring_details: Optional[Dict[str, Any]] = Field(None, description="评分详情")
    confidence_score: Optional[float] = Field(None, ge=0, le=1, description="置信度分数")
    quality_metrics: Optional[Dict[str, Any]] = Field(None, description="质量指标")
    model_name: Optional[str] = Field(None, description="使用的AI模型")
    model_version: Optional[str] = Field(None, description="模型版本")


class AIAnalysisCreate(AIAnalysisBase):
    """创建AI分析Schema"""
    pass


class AIAnalysisUpdate(BaseModel):
    """更新AI分析Schema"""
    analysis_data: Optional[Dict[str, Any]] = Field(None, description="详细分析数据")
    conclusions: Optional[List[str]] = Field(None, description="结论列表")
    suggestions: Optional[List[str]] = Field(None, description="改进建议")
    error_analysis: Optional[Dict[str, Any]] = Field(None, description="错误分析")
    scoring_details: Optional[Dict[str, Any]] = Field(None, description="评分详情")
    confidence_score: Optional[float] = Field(None, ge=0, le=1, description="置信度分数")
    quality_metrics: Optional[Dict[str, Any]] = Field(None, description="质量指标")
    status: Optional[AnalysisStatus] = Field(None, description="分析状态")
    error_message: Optional[str] = Field(None, description="错误信息")


class AIAnalysis(AIAnalysisBase):
    """AI分析Schema"""
    id: int
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    status: AnalysisStatus = Field(AnalysisStatus.COMPLETED, description="分析状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class AIAnalysisRequest(BaseModel):
    """AI分析请求Schema"""
    student_id: str = Field(..., description="学生ID")
    experiment_type: str = Field(..., description="实验类型")
    experiment_data: Dict[str, Any] = Field(..., description="实验数据")
    analysis_type: AnalysisType = Field(AnalysisType.AUTO, description="分析类型")
    model_name: Optional[str] = Field(None, description="指定使用的AI模型")


class AIAnalysisResponse(BaseModel):
    """AI分析响应Schema"""
    success: bool
    message: str
    analysis_id: Optional[int] = None
    analysis_result: Optional[AIAnalysis] = None


class AIAnalysisSummary(BaseModel):
    """AI分析摘要Schema"""
    id: int
    student_id: str
    experiment_type: str
    analysis_type: AnalysisType
    confidence_score: Optional[float] = None
    status: AnalysisStatus
    created_at: datetime

    class Config:
        from_attributes = True
