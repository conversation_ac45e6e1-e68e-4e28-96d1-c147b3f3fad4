from typing import Dict, Any, List, Optional
import numpy as np
import pandas as pd
from datetime import datetime
import json


class DataProcessor:
    """数据处理工具类，用于处理实验数据"""
    
    def __init__(self):
        self.processors = {
            "oscilloscope": self._process_oscilloscope_data,
            "spectrometer": self._process_spectrometer_data,
            "length_measurement": self._process_length_measurement_data,
            "thin_lens": self._process_thin_lens_data,
            # 可以继续添加其他实验的处理函数
        }
    
    async def process_experiment_data(self, experiment_type: str, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理实验数据的主入口"""
        processor = self.processors.get(experiment_type)
        
        if not processor:
            # 如果没有专门的处理器，返回基本处理结果
            return self._basic_data_processing(raw_data)
        
        try:
            return await processor(raw_data)
        except Exception as e:
            print(f"数据处理错误 ({experiment_type}): {e}")
            return self._basic_data_processing(raw_data)
    
    def _basic_data_processing(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """基本数据处理"""
        processed = {
            "processed_at": datetime.now().isoformat(),
            "data_summary": {},
            "calculations": {},
            "validation": {"status": "processed", "errors": []}
        }
        
        # 提取数值数据进行基本统计
        numeric_data = self._extract_numeric_data(raw_data)
        if numeric_data:
            processed["data_summary"] = self._calculate_basic_statistics(numeric_data)
        
        return processed
    
    async def _process_oscilloscope_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理示波器实验数据"""
        processed = {
            "processed_at": datetime.now().isoformat(),
            "experiment_type": "oscilloscope",
            "calculations": {},
            "analysis": {},
            "validation": {"status": "success", "errors": []}
        }
        
        try:
            # 处理表格数据
            if "tableData" in raw_data:
                table_data = raw_data["tableData"]
                processed["calculations"] = self._analyze_oscilloscope_table(table_data)
            
            # 处理设置参数
            if "generator_settings" in raw_data:
                settings = raw_data["generator_settings"]
                processed["generator_analysis"] = self._analyze_generator_settings(settings)
            
            # 处理测量数据
            if "oscilloscope_measurements" in raw_data:
                measurements = raw_data["oscilloscope_measurements"]
                processed["measurement_analysis"] = self._analyze_oscilloscope_measurements(measurements)
            
        except Exception as e:
            processed["validation"]["status"] = "error"
            processed["validation"]["errors"].append(f"数据处理错误: {str(e)}")
        
        return processed
    
    def _analyze_oscilloscope_table(self, table_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析示波器表格数据"""
        if not table_data:
            return {}
        
        # 提取数值数据
        frequencies_set = [row.get("frequency_set", 0) for row in table_data if row.get("frequency_set")]
        frequencies_measured = [row.get("frequency_measured", 0) for row in table_data if row.get("frequency_measured")]
        periods_measured = [row.get("period_measured", 0) for row in table_data if row.get("period_measured")]
        
        calculations = {}
        
        if frequencies_set and frequencies_measured:
            # 计算频率误差
            errors = []
            for i, (f_set, f_measured) in enumerate(zip(frequencies_set, frequencies_measured)):
                if f_set > 0:
                    error = abs(f_measured - f_set) / f_set * 100
                    errors.append(error)
            
            if errors:
                calculations["frequency_errors"] = errors
                calculations["mean_error"] = np.mean(errors)
                calculations["max_error"] = np.max(errors)
                calculations["std_error"] = np.std(errors)
        
        if periods_measured:
            # 计算周期统计
            calculations["period_stats"] = {
                "mean": np.mean(periods_measured),
                "std": np.std(periods_measured),
                "min": np.min(periods_measured),
                "max": np.max(periods_measured)
            }
        
        # 验证频率-周期关系
        if frequencies_measured and periods_measured:
            calculated_frequencies = [1/p if p > 0 else 0 for p in periods_measured]
            frequency_period_errors = []
            for f_measured, f_calculated in zip(frequencies_measured, calculated_frequencies):
                if f_measured > 0:
                    error = abs(f_measured - f_calculated) / f_measured * 100
                    frequency_period_errors.append(error)
            
            if frequency_period_errors:
                calculations["frequency_period_consistency"] = {
                    "errors": frequency_period_errors,
                    "mean_error": np.mean(frequency_period_errors)
                }
        
        return calculations
    
    def _analyze_generator_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """分析函数发生器设置"""
        analysis = {
            "waveform_type": settings.get("waveform_type", "unknown"),
            "frequency_setting": settings.get("frequency_setting", 0),
            "amplitude_setting": settings.get("amplitude_setting", 0),
            "offset_setting": settings.get("offset_setting", 0)
        }
        
        # 添加设置合理性检查
        warnings = []
        
        freq = settings.get("frequency_setting", 0)
        if freq < 1 or freq > 100000:
            warnings.append("频率设置可能超出合理范围")
        
        amp = settings.get("amplitude_setting", 0)
        if amp < 0.1 or amp > 10:
            warnings.append("幅值设置可能超出合理范围")
        
        analysis["warnings"] = warnings
        return analysis
    
    def _analyze_oscilloscope_measurements(self, measurements: Dict[str, Any]) -> Dict[str, Any]:
        """分析示波器测量数据"""
        analysis = {}
        
        # 提取测量值
        freq_measured = measurements.get("measured_frequency", 0)
        period_measured = measurements.get("measured_period", 0)
        vpp_measured = measurements.get("measured_vpp", 0)
        vrms_measured = measurements.get("measured_vrms", 0)
        
        # 验证频率和周期的一致性
        if freq_measured > 0 and period_measured > 0:
            calculated_freq = 1 / period_measured
            freq_error = abs(freq_measured - calculated_freq) / freq_measured * 100
            analysis["frequency_period_consistency"] = {
                "calculated_frequency": calculated_freq,
                "error_percent": freq_error,
                "is_consistent": freq_error < 5  # 5%的误差阈值
            }
        
        # 验证电压测量的合理性
        if vpp_measured > 0 and vrms_measured > 0:
            # 对于正弦波，Vrms = Vpp / (2 * sqrt(2))
            expected_vrms = vpp_measured / (2 * np.sqrt(2))
            voltage_error = abs(vrms_measured - expected_vrms) / vrms_measured * 100 if vrms_measured > 0 else 0
            analysis["voltage_consistency"] = {
                "expected_vrms": expected_vrms,
                "measured_vrms": vrms_measured,
                "error_percent": voltage_error,
                "is_consistent": voltage_error < 10  # 10%的误差阈值
            }
        
        return analysis
    
    async def _process_spectrometer_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理分光计实验数据"""
        # 这里可以添加分光计特定的数据处理逻辑
        return self._basic_data_processing(raw_data)
    
    async def _process_length_measurement_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理长度测量实验数据"""
        # 这里可以添加长度测量特定的数据处理逻辑
        return self._basic_data_processing(raw_data)
    
    async def _process_thin_lens_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理薄透镜实验数据"""
        # 这里可以添加薄透镜特定的数据处理逻辑
        return self._basic_data_processing(raw_data)
    
    def _extract_numeric_data(self, data: Dict[str, Any]) -> Dict[str, List[float]]:
        """从数据中提取数值类型的数据"""
        numeric_data = {}
        
        def extract_recursive(obj, prefix=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    new_key = f"{prefix}.{key}" if prefix else key
                    extract_recursive(value, new_key)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    new_key = f"{prefix}[{i}]" if prefix else f"item_{i}"
                    extract_recursive(item, new_key)
            elif isinstance(obj, (int, float)):
                if prefix not in numeric_data:
                    numeric_data[prefix] = []
                numeric_data[prefix].append(float(obj))
        
        extract_recursive(data)
        return numeric_data
    
    def _calculate_basic_statistics(self, numeric_data: Dict[str, List[float]]) -> Dict[str, Any]:
        """计算基本统计信息"""
        statistics = {}
        
        for key, values in numeric_data.items():
            if values:
                statistics[key] = {
                    "count": len(values),
                    "mean": np.mean(values),
                    "std": np.std(values),
                    "min": np.min(values),
                    "max": np.max(values),
                    "median": np.median(values)
                }
        
        return statistics
