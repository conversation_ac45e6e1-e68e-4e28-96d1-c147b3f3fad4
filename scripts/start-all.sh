#!/bin/bash

# 物理实验平台启动脚本
# 用于启动所有服务容器

set -e

echo "🚀 启动物理实验平台..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装，请先安装docker-compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p backend/logs
mkdir -p backend/static/uploads
mkdir -p backend/static/plots
mkdir -p database/logs
mkdir -p nginx/ssl

# 设置权限
chmod -R 755 backend/logs
chmod -R 755 backend/static
chmod -R 755 database/logs

# 停止现有容器（如果存在）
echo "🛑 停止现有容器..."
docker-compose down --remove-orphans

# 清理未使用的镜像和容器（可选）
read -p "是否清理未使用的Docker资源？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理Docker资源..."
    docker system prune -f
fi

# 构建镜像
echo "🔨 构建镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查服务健康状态
echo "🏥 检查服务健康状态..."

# 检查数据库
echo "检查数据库连接..."
if docker-compose exec -T database mysqladmin ping -h localhost -u root -pphysics_root_2024 --silent; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
fi

# 检查Redis
echo "检查Redis连接..."
if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
fi

# 检查后端API
echo "检查后端API..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端API正常"
else
    echo "❌ 后端API异常"
fi

# 检查前端
echo "检查前端服务..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
fi

# 检查管理后台
echo "检查管理后台..."
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ 管理后台正常"
else
    echo "❌ 管理后台异常"
fi

echo ""
echo "🎉 物理实验平台启动完成！"
echo ""
echo "📋 服务访问地址："
echo "   前端应用:    http://localhost:3000"
echo "   管理后台:    http://localhost:8080"
echo "   API文档:     http://localhost:8000/docs"
echo "   数据库:      localhost:3306"
echo "   Redis:       localhost:6379"
echo ""
echo "📊 查看日志："
echo "   docker-compose logs -f [service_name]"
echo ""
echo "🛑 停止服务："
echo "   docker-compose down"
echo ""

# 显示容器状态
echo "📈 当前容器状态："
docker-compose ps
