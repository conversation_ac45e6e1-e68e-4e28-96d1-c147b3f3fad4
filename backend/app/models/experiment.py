from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Float, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

from app.core.database import Base


class Student(Base):
    """学生模型"""
    __tablename__ = "students"
    
    id = Column(String(50), primary_key=True, index=True)  # 学号
    name = Column(String(100), nullable=False)  # 姓名
    email = Column(String(255), nullable=True)  # 邮箱
    phone = Column(String(20), nullable=True)  # 电话
    class_name = Column(String(100), nullable=True)  # 班级
    major = Column(String(100), nullable=True)  # 专业
    grade = Column(String(20), nullable=True)  # 年级
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    experiments = relationship("ExperimentRecord", back_populates="student")


class ExperimentRecord(Base):
    """实验记录模型"""
    __tablename__ = "experiment_records"
    
    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(50), ForeignKey("students.id"), nullable=False)
    experiment_type = Column(String(100), nullable=False, index=True)  # 实验类型
    experiment_name = Column(String(200), nullable=False)  # 实验名称
    version = Column(Integer, default=1)  # 版本号
    status = Column(String(50), default="submitted")  # 状态: submitted, reviewed, approved
    
    # 实验数据
    raw_data = Column(JSON)  # 原始数据
    processed_data = Column(JSON)  # 处理后的数据
    analysis_result = Column(JSON)  # 分析结果
    charts_data = Column(JSON)  # 图表数据
    
    # 评分相关
    score = Column(Float, nullable=True)  # 分数
    feedback = Column(Text, nullable=True)  # 反馈
    reviewer_id = Column(String(50), nullable=True)  # 评阅者ID
    reviewed_at = Column(DateTime(timezone=True), nullable=True)  # 评阅时间
    
    # 时间戳
    submit_time = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    student = relationship("Student", back_populates="experiments")


class AIModel(Base):
    """AI模型配置"""
    __tablename__ = "ai_models"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)  # 模型显示名称
    internal_name = Column(String(100), nullable=False)  # 模型内部名称
    provider = Column(String(50), nullable=False)  # 提供商: openai, anthropic, etc.
    api_key = Column(String(500), nullable=False)  # API密钥
    base_url = Column(String(500), nullable=True)  # API基础URL
    max_tokens = Column(Integer, default=4000)  # 最大token数
    temperature = Column(Float, default=0.7)  # 温度参数
    is_active = Column(Boolean, default=True)  # 是否启用
    is_default = Column(Boolean, default=False)  # 是否为默认模型
    description = Column(Text, nullable=True)  # 描述
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ExperimentTemplate(Base):
    """实验模板"""
    __tablename__ = "experiment_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(String(100), unique=True, nullable=False, index=True)  # 实验ID
    name = Column(String(200), nullable=False)  # 实验名称
    description = Column(Text, nullable=True)  # 实验描述
    category = Column(String(100), nullable=False)  # 实验分类
    difficulty = Column(String(20), default="medium")  # 难度: easy, medium, hard
    duration = Column(Integer, default=60)  # 预计时长(分钟)
    tags = Column(JSON)  # 标签列表
    
    # 实验配置
    config = Column(JSON, nullable=False)  # 实验配置JSON
    steps_config = Column(JSON, nullable=False)  # 步骤配置
    
    # 状态
    is_active = Column(Boolean, default=True)  # 是否启用
    version = Column(String(20), default="1.0.0")  # 版本号
    
    # 统计信息
    total_submissions = Column(Integer, default=0)  # 总提交数
    average_score = Column(Float, nullable=True)  # 平均分数
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class SystemLog(Base):
    """系统日志"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(20), nullable=False)  # 日志级别: DEBUG, INFO, WARNING, ERROR
    message = Column(Text, nullable=False)  # 日志消息
    module = Column(String(100), nullable=True)  # 模块名
    function = Column(String(100), nullable=True)  # 函数名
    user_id = Column(String(50), nullable=True)  # 用户ID
    ip_address = Column(String(45), nullable=True)  # IP地址
    user_agent = Column(String(500), nullable=True)  # 用户代理
    extra_data = Column(JSON, nullable=True)  # 额外数据
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class FileUpload(Base):
    """文件上传记录"""
    __tablename__ = "file_uploads"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)  # 原始文件名
    stored_filename = Column(String(255), nullable=False)  # 存储文件名
    file_path = Column(String(500), nullable=False)  # 文件路径
    file_size = Column(Integer, nullable=False)  # 文件大小(字节)
    mime_type = Column(String(100), nullable=True)  # MIME类型
    uploader_id = Column(String(50), nullable=True)  # 上传者ID
    experiment_id = Column(String(100), nullable=True)  # 关联实验ID
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
