from pydantic import BaseModel, Field
from typing import List
from datetime import datetime


class DashboardStats(BaseModel):
    """仪表盘统计数据Schema"""
    total_students: int = Field(0, description="学生总数")
    total_classes: int = Field(0, description="班级总数")
    total_experiments: int = Field(0, description="实验总数")
    total_records: int = Field(0, description="实验记录总数")
    today_submissions: int = Field(0, description="今日提交数")
    pending_reviews: int = Field(0, description="待审核数")
    pass_rate: float = Field(0.0, description="通过率")
    average_score: float = Field(0.0, description="平均分")


class ActivityRecord(BaseModel):
    """活动记录Schema"""
    id: int
    time: str = Field(..., description="时间")
    student_id: str = Field(..., description="学生ID")
    student_name: str = Field(..., description="学生姓名")
    experiment_type: str = Field(..., description="实验类型")
    experiment_name: str = Field(..., description="实验名称")
    action: str = Field(..., description="操作")
    status: str = Field(..., description="状态")


class SubmissionTrend(BaseModel):
    """提交趋势Schema"""
    dates: List[str] = Field(..., description="日期列表")
    counts: List[int] = Field(..., description="提交数量列表")


class GradeDistribution(BaseModel):
    """成绩分布Schema"""
    ranges: List[str] = Field(..., description="分数区间")
    counts: List[int] = Field(..., description="各区间数量")


class PassRateStats(BaseModel):
    """通过率统计Schema"""
    passed: int = Field(0, description="通过数量")
    failed: int = Field(0, description="未通过数量")
    total: int = Field(0, description="总数量")


class ChartData(BaseModel):
    """图表数据Schema"""
    submission_trend: SubmissionTrend
    grade_distribution: GradeDistribution
    pass_rate_data: PassRateStats
