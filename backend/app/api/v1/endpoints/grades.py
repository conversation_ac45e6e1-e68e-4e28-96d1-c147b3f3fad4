from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.schemas.grade_schema import (
    GradeUpdate,
    GradeResponse,
    PassStatusUpdate,
    GradeStatistics,
    ClassGradesSummary
)
from app.services.grade_service import GradeService

router = APIRouter()


@router.put("/records/{record_id}/score", response_model=GradeResponse)
async def update_experiment_score(
    record_id: int,
    grade_update: GradeUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新实验记录分数"""
    grade_service = GradeService(db)
    
    result = await grade_service.update_experiment_score(record_id, grade_update)
    if not result:
        raise HTTPException(status_code=404, detail="实验记录不存在")
    
    return result


@router.put("/records/{record_id}/pass-status", response_model=GradeResponse)
async def update_pass_status(
    record_id: int,
    status_update: PassStatusUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新通过状态"""
    grade_service = GradeService(db)
    
    result = await grade_service.update_pass_status(record_id, status_update)
    if not result:
        raise HTTPException(status_code=404, detail="实验记录不存在")
    
    return result


@router.post("/records/{record_id}/auto-grade")
async def auto_grade_experiment(
    record_id: int,
    use_ai_score: bool = Query(True, description="是否使用AI评分"),
    db: AsyncSession = Depends(get_db)
):
    """自动评分实验"""
    grade_service = GradeService(db)
    
    try:
        result = await grade_service.auto_grade_experiment(record_id, use_ai_score)
        if not result:
            raise HTTPException(status_code=404, detail="实验记录不存在")
        
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"自动评分失败: {str(e)}")


@router.get("/students/{student_id}/statistics", response_model=GradeStatistics)
async def get_student_grade_statistics(
    student_id: str,
    experiment_type: Optional[str] = Query(None, description="实验类型筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取学生成绩统计"""
    grade_service = GradeService(db)
    
    stats = await grade_service.get_student_grade_statistics(student_id, experiment_type)
    if not stats:
        raise HTTPException(status_code=404, detail="学生不存在或无实验记录")
    
    return stats


@router.get("/classes/{class_id}/statistics", response_model=ClassGradesSummary)
async def get_class_grade_statistics(
    class_id: int,
    experiment_type: Optional[str] = Query(None, description="实验类型筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取班级成绩统计"""
    grade_service = GradeService(db)
    
    stats = await grade_service.get_class_grade_statistics(class_id, experiment_type)
    if not stats:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    return stats


@router.get("/experiments/{experiment_type}/statistics")
async def get_experiment_grade_statistics(
    experiment_type: str,
    class_id: Optional[int] = Query(None, description="班级ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取实验成绩统计"""
    grade_service = GradeService(db)
    
    stats = await grade_service.get_experiment_grade_statistics(experiment_type, class_id)
    return {
        "experiment_type": experiment_type,
        "statistics": stats
    }


@router.post("/batch/auto-grade")
async def batch_auto_grade(
    experiment_type: Optional[str] = Query(None, description="实验类型"),
    class_id: Optional[int] = Query(None, description="班级ID"),
    student_ids: Optional[List[str]] = Query(None, description="学生ID列表"),
    use_ai_score: bool = Query(True, description="是否使用AI评分"),
    db: AsyncSession = Depends(get_db)
):
    """批量自动评分"""
    grade_service = GradeService(db)
    
    try:
        result = await grade_service.batch_auto_grade(
            experiment_type=experiment_type,
            class_id=class_id,
            student_ids=student_ids,
            use_ai_score=use_ai_score
        )
        
        return {
            "message": "批量评分完成",
            "processed_count": result["processed_count"],
            "success_count": result["success_count"],
            "failed_count": result["failed_count"],
            "failed_records": result.get("failed_records", [])
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量评分失败: {str(e)}")


@router.get("/pending-review")
async def get_pending_review_records(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    experiment_type: Optional[str] = Query(None, description="实验类型筛选"),
    class_id: Optional[int] = Query(None, description="班级ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取待审核的实验记录"""
    grade_service = GradeService(db)
    
    records = await grade_service.get_pending_review_records(
        page=page,
        size=size,
        experiment_type=experiment_type,
        class_id=class_id
    )
    
    return {
        "records": records,
        "pagination": {
            "page": page,
            "size": size,
            "total": len(records)  # 这里应该返回实际的总数
        }
    }


@router.post("/records/{record_id}/review")
async def review_experiment_record(
    record_id: int,
    grade_update: GradeUpdate,
    db: AsyncSession = Depends(get_db)
):
    """审核实验记录"""
    grade_service = GradeService(db)
    
    try:
        result = await grade_service.review_experiment_record(record_id, grade_update)
        if not result:
            raise HTTPException(status_code=404, detail="实验记录不存在")
        
        return {
            "message": "审核完成",
            "record": result
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"审核失败: {str(e)}")


@router.get("/export/class/{class_id}/grades")
async def export_class_grades(
    class_id: int,
    experiment_type: Optional[str] = Query(None, description="实验类型筛选"),
    format: str = Query("csv", description="导出格式: csv, excel"),
    db: AsyncSession = Depends(get_db)
):
    """导出班级成绩"""
    grade_service = GradeService(db)
    
    try:
        file_data = await grade_service.export_class_grades(
            class_id=class_id,
            experiment_type=experiment_type,
            format=format
        )
        
        if not file_data:
            raise HTTPException(status_code=404, detail="班级不存在或无数据")
        
        return {
            "message": "导出成功",
            "file_url": file_data["file_url"],
            "file_name": file_data["file_name"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


@router.get("/thresholds/experiment/{experiment_type}")
async def get_experiment_pass_thresholds(
    experiment_type: str,
    db: AsyncSession = Depends(get_db)
):
    """获取实验通过阈值"""
    grade_service = GradeService(db)
    
    thresholds = await grade_service.get_experiment_pass_thresholds(experiment_type)
    return {
        "experiment_type": experiment_type,
        "thresholds": thresholds
    }


@router.put("/thresholds/experiment/{experiment_type}")
async def update_experiment_pass_thresholds(
    experiment_type: str,
    thresholds: dict,
    db: AsyncSession = Depends(get_db)
):
    """更新实验通过阈值"""
    grade_service = GradeService(db)
    
    try:
        result = await grade_service.update_experiment_pass_thresholds(experiment_type, thresholds)
        if not result:
            raise HTTPException(status_code=404, detail="实验模板不存在")
        
        return {
            "message": "阈值更新成功",
            "experiment_type": experiment_type,
            "thresholds": thresholds
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")
