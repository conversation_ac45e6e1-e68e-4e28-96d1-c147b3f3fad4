from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from typing import List, Dict, Any
from datetime import datetime, timedelta

from app.core.database import get_db
from app.models.experiment import Student, Class, ExperimentRecord, ExperimentTemplate, PassStatus
from app.schemas.dashboard_schema import (
    DashboardStats,
    ActivityRecord,
    ChartData,
    SubmissionTrend,
    GradeDistribution,
    PassRateStats
)

router = APIRouter()


@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    db: AsyncSession = Depends(get_db)
):
    """获取仪表盘统计数据"""
    
    # 学生总数
    total_students_result = await db.execute(
        select(func.count(Student.id)).where(Student.is_active == True)
    )
    total_students = total_students_result.scalar() or 0
    
    # 班级总数
    total_classes_result = await db.execute(
        select(func.count(Class.id)).where(Class.is_active == True)
    )
    total_classes = total_classes_result.scalar() or 0
    
    # 实验总数
    total_experiments_result = await db.execute(
        select(func.count(ExperimentTemplate.id)).where(ExperimentTemplate.is_active == True)
    )
    total_experiments = total_experiments_result.scalar() or 0
    
    # 实验记录总数
    total_records_result = await db.execute(
        select(func.count(ExperimentRecord.id))
    )
    total_records = total_records_result.scalar() or 0
    
    # 今日提交数
    today = datetime.now().date()
    today_submissions_result = await db.execute(
        select(func.count(ExperimentRecord.id)).where(
            func.date(ExperimentRecord.submit_time) == today
        )
    )
    today_submissions = today_submissions_result.scalar() or 0
    
    # 待审核数
    pending_reviews_result = await db.execute(
        select(func.count(ExperimentRecord.id)).where(
            ExperimentRecord.pass_status == PassStatus.PENDING
        )
    )
    pending_reviews = pending_reviews_result.scalar() or 0
    
    # 通过率
    total_graded_result = await db.execute(
        select(func.count(ExperimentRecord.id)).where(
            ExperimentRecord.pass_status.in_([PassStatus.PASSED, PassStatus.FAILED])
        )
    )
    total_graded = total_graded_result.scalar() or 0
    
    passed_result = await db.execute(
        select(func.count(ExperimentRecord.id)).where(
            ExperimentRecord.pass_status == PassStatus.PASSED
        )
    )
    passed = passed_result.scalar() or 0
    
    pass_rate = (passed / total_graded * 100) if total_graded > 0 else 0
    
    # 平均分
    avg_score_result = await db.execute(
        select(func.avg(ExperimentRecord.score)).where(
            ExperimentRecord.score.is_not(None)
        )
    )
    average_score = avg_score_result.scalar() or 0
    
    return DashboardStats(
        total_students=total_students,
        total_classes=total_classes,
        total_experiments=total_experiments,
        total_records=total_records,
        today_submissions=today_submissions,
        pending_reviews=pending_reviews,
        pass_rate=round(pass_rate, 2),
        average_score=round(float(average_score), 2)
    )


@router.get("/activities", response_model=List[ActivityRecord])
async def get_recent_activities(
    limit: int = Query(10, ge=1, le=50, description="返回记录数量"),
    db: AsyncSession = Depends(get_db)
):
    """获取最近活动记录"""
    
    result = await db.execute(
        select(
            ExperimentRecord.id,
            ExperimentRecord.submit_time,
            ExperimentRecord.student_id,
            Student.name.label('student_name'),
            ExperimentRecord.experiment_type,
            ExperimentRecord.experiment_name,
            ExperimentRecord.status,
            ExperimentRecord.pass_status
        )
        .join(Student, ExperimentRecord.student_id == Student.id)
        .order_by(ExperimentRecord.submit_time.desc())
        .limit(limit)
    )
    
    activities = []
    for row in result:
        activities.append(ActivityRecord(
            id=row.id,
            time=row.submit_time.strftime("%H:%M"),
            student_id=row.student_id,
            student_name=row.student_name,
            experiment_type=row.experiment_type,
            experiment_name=row.experiment_name,
            action="提交",
            status=_get_status_text(row.status, row.pass_status)
        ))
    
    return activities


@router.get("/submission-trend", response_model=SubmissionTrend)
async def get_submission_trend(
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    db: AsyncSession = Depends(get_db)
):
    """获取实验提交趋势"""
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days-1)
    
    result = await db.execute(
        select(
            func.date(ExperimentRecord.submit_time).label('date'),
            func.count(ExperimentRecord.id).label('count')
        )
        .where(
            and_(
                func.date(ExperimentRecord.submit_time) >= start_date,
                func.date(ExperimentRecord.submit_time) <= end_date
            )
        )
        .group_by(func.date(ExperimentRecord.submit_time))
        .order_by(func.date(ExperimentRecord.submit_time))
    )
    
    # 创建完整的日期序列
    dates = []
    counts = []
    data_dict = {row.date: row.count for row in result}
    
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime("%m-%d"))
        counts.append(data_dict.get(current_date, 0))
        current_date += timedelta(days=1)
    
    return SubmissionTrend(dates=dates, counts=counts)


@router.get("/grade-distribution", response_model=GradeDistribution)
async def get_grade_distribution(
    db: AsyncSession = Depends(get_db)
):
    """获取成绩分布"""
    
    result = await db.execute(
        select(ExperimentRecord.score)
        .where(ExperimentRecord.score.is_not(None))
    )
    
    scores = [row.score for row in result]
    
    # 分数区间统计
    ranges = ["0-59", "60-69", "70-79", "80-89", "90-100"]
    counts = [0, 0, 0, 0, 0]
    
    for score in scores:
        if score < 60:
            counts[0] += 1
        elif score < 70:
            counts[1] += 1
        elif score < 80:
            counts[2] += 1
        elif score < 90:
            counts[3] += 1
        else:
            counts[4] += 1
    
    return GradeDistribution(ranges=ranges, counts=counts)


@router.get("/pass-rate", response_model=PassRateStats)
async def get_pass_rate_stats(
    db: AsyncSession = Depends(get_db)
):
    """获取通过率统计"""
    
    # 已通过数量
    passed_result = await db.execute(
        select(func.count(ExperimentRecord.id)).where(
            ExperimentRecord.pass_status == PassStatus.PASSED
        )
    )
    passed = passed_result.scalar() or 0
    
    # 未通过数量
    failed_result = await db.execute(
        select(func.count(ExperimentRecord.id)).where(
            ExperimentRecord.pass_status == PassStatus.FAILED
        )
    )
    failed = failed_result.scalar() or 0
    
    total = passed + failed
    
    return PassRateStats(
        passed=passed,
        failed=failed,
        total=total
    )


@router.get("/charts", response_model=ChartData)
async def get_chart_data(
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    db: AsyncSession = Depends(get_db)
):
    """获取图表数据"""
    
    # 获取提交趋势
    submission_trend = await get_submission_trend(days, db)
    
    # 获取成绩分布
    grade_distribution = await get_grade_distribution(db)
    
    # 获取通过率
    pass_rate_data = await get_pass_rate_stats(db)
    
    return ChartData(
        submission_trend=submission_trend,
        grade_distribution=grade_distribution,
        pass_rate_data=pass_rate_data
    )


def _get_status_text(status: str, pass_status: PassStatus) -> str:
    """获取状态文本"""
    if status == "approved":
        return "已完成"
    elif pass_status == PassStatus.PASSED:
        return "已通过"
    elif pass_status == PassStatus.FAILED:
        return "未通过"
    elif pass_status == PassStatus.REVIEWING:
        return "审核中"
    else:
        return "待审核"
