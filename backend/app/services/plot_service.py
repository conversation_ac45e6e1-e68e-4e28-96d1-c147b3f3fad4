from typing import Dict, Any, Optional
import json
import base64
import io
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

from app.schemas.experiment import PlotRequest, PlotResponse
from app.core.config import settings


class PlotService:
    def __init__(self):
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

    async def generate_plot(self, plot_request: PlotRequest) -> PlotResponse:
        """生成图表"""
        try:
            experiment_type = plot_request.experiment_type
            data = plot_request.data
            config = plot_request.config or {}
            
            # 根据实验类型生成不同的图表
            if experiment_type == "oscilloscope":
                return await self._generate_oscilloscope_plot(data, config)
            elif experiment_type == "analysis":
                return await self._generate_analysis_plot(data, config)
            else:
                return await self._generate_generic_plot(data, config)
                
        except Exception as e:
            return PlotResponse(
                success=False,
                message=f"图表生成失败: {str(e)}"
            )

    async def _generate_oscilloscope_plot(self, data: Dict[str, Any], config: Dict[str, Any]) -> PlotResponse:
        """生成示波器实验图表"""
        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
            
            # 提取数据
            table_data = data.get('tableData', [])
            if not table_data:
                return PlotResponse(
                    success=False,
                    message="没有找到表格数据"
                )
            
            frequencies_set = [row.get('frequency_set', 0) for row in table_data]
            frequencies_measured = [row.get('frequency_measured', 0) for row in table_data]
            errors = [abs(m - s) / s * 100 if s > 0 else 0 
                     for s, m in zip(frequencies_set, frequencies_measured)]
            
            # 第一个子图：频率对比
            ax1.scatter(frequencies_set, frequencies_measured, color='blue', s=50, alpha=0.7, label='测量数据')
            
            # 绘制理想直线
            if frequencies_set:
                min_freq = min(frequencies_set)
                max_freq = max(frequencies_set)
                ax1.plot([min_freq, max_freq], [min_freq, max_freq], 
                        'r--', alpha=0.8, label='理想直线')
            
            ax1.set_xlabel('设定频率 (Hz)')
            ax1.set_ylabel('测量频率 (Hz)')
            ax1.set_title('设定频率与测量频率对比')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 第二个子图：误差分析
            x_pos = range(len(errors))
            bars = ax2.bar(x_pos, errors, color='orange', alpha=0.7)
            ax2.set_xlabel('测量点')
            ax2.set_ylabel('相对误差 (%)')
            ax2.set_title('频率测量相对误差')
            ax2.set_xticks(x_pos)
            ax2.set_xticklabels([f'点{i+1}' for i in x_pos])
            ax2.grid(True, alpha=0.3)
            
            # 添加误差值标签
            for bar, error in zip(bars, errors):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{error:.2f}%', ha='center', va='bottom', fontsize=9)
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=settings.PLOT_DPI, bbox_inches='tight')
            buffer.seek(0)
            plot_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return PlotResponse(
                success=True,
                plot_data=f"data:image/png;base64,{plot_data}"
            )
            
        except Exception as e:
            return PlotResponse(
                success=False,
                message=f"示波器图表生成失败: {str(e)}"
            )

    async def _generate_analysis_plot(self, data: Dict[str, Any], config: Dict[str, Any]) -> PlotResponse:
        """生成分析图表"""
        try:
            chart_type = config.get('type', 'scatter')
            
            if chart_type == 'scatter':
                return await self._generate_scatter_plot(data, config)
            elif chart_type == 'bar':
                return await self._generate_bar_plot(data, config)
            else:
                return await self._generate_generic_plot(data, config)
                
        except Exception as e:
            return PlotResponse(
                success=False,
                message=f"分析图表生成失败: {str(e)}"
            )

    async def _generate_scatter_plot(self, data: Dict[str, Any], config: Dict[str, Any]) -> PlotResponse:
        """生成散点图"""
        try:
            fig, ax = plt.subplots(figsize=(8, 6))
            
            # 从配置中获取数据
            x_data = data.get('x', [])
            y_data = data.get('y', [])
            
            if not x_data or not y_data:
                # 生成示例数据
                x_data = np.linspace(0, 10, 20)
                y_data = x_data + np.random.normal(0, 0.5, len(x_data))
            
            ax.scatter(x_data, y_data, alpha=0.7, s=50)
            
            # 设置标题和标签
            title = config.get('title', '散点图')
            x_title = config.get('xAxis', {}).get('title', 'X轴')
            y_title = config.get('yAxis', {}).get('title', 'Y轴')
            
            ax.set_title(title)
            ax.set_xlabel(x_title)
            ax.set_ylabel(y_title)
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=settings.PLOT_DPI, bbox_inches='tight')
            buffer.seek(0)
            plot_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return PlotResponse(
                success=True,
                plot_data=f"data:image/png;base64,{plot_data}"
            )
            
        except Exception as e:
            return PlotResponse(
                success=False,
                message=f"散点图生成失败: {str(e)}"
            )

    async def _generate_bar_plot(self, data: Dict[str, Any], config: Dict[str, Any]) -> PlotResponse:
        """生成柱状图"""
        try:
            fig, ax = plt.subplots(figsize=(8, 6))
            
            # 从配置中获取数据
            x_data = data.get('x', [])
            y_data = data.get('y', [])
            
            if not x_data or not y_data:
                # 生成示例数据
                x_data = ['A', 'B', 'C', 'D', 'E']
                y_data = [23, 45, 56, 78, 32]
            
            bars = ax.bar(x_data, y_data, alpha=0.7)
            
            # 设置标题和标签
            title = config.get('title', '柱状图')
            x_title = config.get('xAxis', {}).get('title', 'X轴')
            y_title = config.get('yAxis', {}).get('title', 'Y轴')
            
            ax.set_title(title)
            ax.set_xlabel(x_title)
            ax.set_ylabel(y_title)
            ax.grid(True, alpha=0.3, axis='y')
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.1f}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=settings.PLOT_DPI, bbox_inches='tight')
            buffer.seek(0)
            plot_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return PlotResponse(
                success=True,
                plot_data=f"data:image/png;base64,{plot_data}"
            )
            
        except Exception as e:
            return PlotResponse(
                success=False,
                message=f"柱状图生成失败: {str(e)}"
            )

    async def _generate_generic_plot(self, data: Dict[str, Any], config: Dict[str, Any]) -> PlotResponse:
        """生成通用图表"""
        try:
            fig, ax = plt.subplots(figsize=(8, 6))
            
            # 生成示例数据
            x = np.linspace(0, 2*np.pi, 100)
            y = np.sin(x)
            
            ax.plot(x, y, 'b-', linewidth=2, label='sin(x)')
            ax.set_title('示例图表')
            ax.set_xlabel('X轴')
            ax.set_ylabel('Y轴')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 转换为base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=settings.PLOT_DPI, bbox_inches='tight')
            buffer.seek(0)
            plot_data = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return PlotResponse(
                success=True,
                plot_data=f"data:image/png;base64,{plot_data}"
            )
            
        except Exception as e:
            return PlotResponse(
                success=False,
                message=f"通用图表生成失败: {str(e)}"
            )
