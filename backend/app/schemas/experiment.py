from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class DifficultyLevel(str, Enum):
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"


class ExperimentStatus(str, Enum):
    SUBMITTED = "submitted"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"


# 学生相关Schema
class StudentBase(BaseModel):
    id: str = Field(..., description="学号")
    name: str = Field(..., description="姓名")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="电话")
    class_name: Optional[str] = Field(None, description="班级")
    major: Optional[str] = Field(None, description="专业")
    grade: Optional[str] = Field(None, description="年级")


class StudentCreate(StudentBase):
    pass


class StudentUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    class_name: Optional[str] = None
    major: Optional[str] = None
    grade: Optional[str] = None


class Student(StudentBase):
    model_config = ConfigDict(from_attributes=True)
    
    created_at: datetime
    updated_at: Optional[datetime] = None


class StudentValidation(BaseModel):
    exists: bool
    name: Optional[str] = None


# 实验配置相关Schema
class ExperimentInfo(BaseModel):
    id: str
    name: str
    description: str
    category: str
    difficulty: DifficultyLevel
    duration: int  # 分钟
    tags: List[str]


class FormField(BaseModel):
    name: str
    label: str
    type: str
    required: Optional[bool] = False
    placeholder: Optional[str] = None
    unit: Optional[str] = None
    min: Optional[float] = None
    max: Optional[float] = None
    step: Optional[float] = None
    precision: Optional[int] = None
    options: Optional[List[Dict[str, Any]]] = None
    validation: Optional[List[Dict[str, Any]]] = None
    hint: Optional[str] = None


class ExperimentStep(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    component: str
    config: Dict[str, Any]
    validation: Optional[List[Dict[str, Any]]] = None
    hints: Optional[List[str]] = None


class ExperimentConfig(BaseModel):
    info: ExperimentInfo
    steps: List[ExperimentStep]
    theme: Optional[Dict[str, str]] = None


# 实验提交相关Schema
class ExperimentSubmission(BaseModel):
    experiment_id: str = Field(..., description="实验ID")
    student_info: Dict[str, str] = Field(..., description="学生信息")
    step_data: Dict[str, Any] = Field(..., description="步骤数据")
    charts: Optional[Dict[str, Any]] = Field(None, description="图表数据")
    analysis: Optional[Dict[str, Any]] = Field(None, description="分析结果")
    submit_time: Optional[str] = Field(None, description="提交时间")


class ExperimentSubmissionResponse(BaseModel):
    success: bool
    message: str
    record_id: Optional[int] = None
    version: Optional[int] = None


# 实验记录相关Schema
class ExperimentRecordBase(BaseModel):
    student_id: str
    experiment_type: str
    experiment_name: str
    status: ExperimentStatus = ExperimentStatus.SUBMITTED
    raw_data: Optional[Dict[str, Any]] = None
    processed_data: Optional[Dict[str, Any]] = None
    analysis_result: Optional[Dict[str, Any]] = None
    charts_data: Optional[Dict[str, Any]] = None


class ExperimentRecordCreate(ExperimentRecordBase):
    pass


class ExperimentRecordUpdate(BaseModel):
    status: Optional[ExperimentStatus] = None
    score: Optional[float] = None
    feedback: Optional[str] = None
    reviewer_id: Optional[str] = None


class ExperimentRecord(ExperimentRecordBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    version: int
    score: Optional[float] = None
    feedback: Optional[str] = None
    reviewer_id: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    submit_time: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None


# 图表生成相关Schema
class PlotRequest(BaseModel):
    experiment_type: str = Field(..., description="实验类型")
    data: Dict[str, Any] = Field(..., description="绘图数据")
    config: Optional[Dict[str, Any]] = Field(None, description="绘图配置")


class PlotResponse(BaseModel):
    success: bool
    plot_url: Optional[str] = None
    plot_data: Optional[str] = None  # base64编码的图片数据
    message: Optional[str] = None


# AI分析相关Schema
class AnalysisRequest(BaseModel):
    experiment_type: str = Field(..., description="实验类型")
    data: Dict[str, Any] = Field(..., description="分析数据")
    model_id: Optional[str] = Field(None, description="AI模型ID")
    custom_prompt: Optional[str] = Field(None, description="自定义提示")


class AnalysisResponse(BaseModel):
    success: bool
    text: Optional[str] = None
    conclusion: Optional[str] = None
    suggestions: Optional[List[str]] = None
    model_used: Optional[str] = None
    message: Optional[str] = None


# AI模型相关Schema
class AIModelBase(BaseModel):
    name: str
    internal_name: str
    provider: str
    api_key: str
    base_url: Optional[str] = None
    max_tokens: int = 4000
    temperature: float = 0.7
    is_active: bool = True
    is_default: bool = False
    description: Optional[str] = None


class AIModelCreate(AIModelBase):
    pass


class AIModelUpdate(BaseModel):
    name: Optional[str] = None
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    description: Optional[str] = None


class AIModel(AIModelBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None


class AIModelPublic(BaseModel):
    """公开的AI模型信息（不包含敏感信息）"""
    id: int
    name: str
    provider: str
    is_active: bool
    is_default: bool
    description: Optional[str] = None


# 实验模板相关Schema
class ExperimentTemplateBase(BaseModel):
    experiment_id: str
    name: str
    description: Optional[str] = None
    category: str
    difficulty: DifficultyLevel = DifficultyLevel.MEDIUM
    duration: int = 60
    tags: Optional[List[str]] = None
    config: Dict[str, Any]
    steps_config: Dict[str, Any]
    is_active: bool = True
    version: str = "1.0.0"


class ExperimentTemplateCreate(ExperimentTemplateBase):
    pass


class ExperimentTemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    difficulty: Optional[DifficultyLevel] = None
    duration: Optional[int] = None
    tags: Optional[List[str]] = None
    config: Optional[Dict[str, Any]] = None
    steps_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    version: Optional[str] = None


class ExperimentTemplate(ExperimentTemplateBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    total_submissions: int
    average_score: Optional[float] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


class ExperimentListItem(BaseModel):
    """实验列表项"""
    id: str
    name: str
    description: str
    category: str
    difficulty: DifficultyLevel
    duration: int
    tags: List[str]


# 通用响应Schema
class MessageResponse(BaseModel):
    message: str


class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int
