from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
import os
import uuid
import aiofiles
from pathlib import Path

from app.core.database import get_db
from app.core.config import settings
from app.services.file_service import FileService

router = APIRouter()


@router.post("/")
async def upload_file(
    file: UploadFile = File(...),
    experiment_id: Optional[str] = Form(None),
    uploader_id: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db)
):
    """上传文件"""
    file_service = FileService(db)
    
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件大小
        if file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400, 
                detail=f"文件大小超过限制 ({settings.MAX_FILE_SIZE} bytes)"
            )
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in settings.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_ext}"
            )
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)
        
        # 确保上传目录存在
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # 记录到数据库
        file_record = await file_service.create_file_record(
            filename=file.filename,
            stored_filename=unique_filename,
            file_path=file_path,
            file_size=len(content),
            mime_type=file.content_type,
            uploader_id=uploader_id,
            experiment_id=experiment_id
        )
        
        return {
            "success": True,
            "message": "文件上传成功",
            "file_id": file_record.id,
            "filename": file.filename,
            "url": f"/static/uploads/{unique_filename}",
            "size": len(content)
        }
        
    except Exception as e:
        # 如果出错，删除已上传的文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        
        if isinstance(e, HTTPException):
            raise e
        else:
            raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@router.get("/files/{file_id}")
async def get_file_info(
    file_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取文件信息"""
    file_service = FileService(db)
    file_info = await file_service.get_file_info(file_id)
    
    if not file_info:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    return file_info


@router.delete("/files/{file_id}")
async def delete_file(
    file_id: int,
    db: AsyncSession = Depends(get_db)
):
    """删除文件"""
    file_service = FileService(db)
    success = await file_service.delete_file(file_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    return {"message": "文件删除成功"}
