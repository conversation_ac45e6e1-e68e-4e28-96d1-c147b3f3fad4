from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List
from datetime import datetime


class StudentBase(BaseModel):
    """学生基础Schema"""
    id: str = Field(..., description="学号", max_length=50)
    name: str = Field(..., description="姓名", max_length=100)
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="电话", max_length=20)
    class_id: Optional[int] = Field(None, description="班级ID")
    major: Optional[str] = Field(None, description="专业", max_length=100)
    grade: Optional[str] = Field(None, description="年级", max_length=20)
    enrollment_year: Optional[str] = Field(None, description="入学年份", max_length=10)
    is_active: bool = Field(True, description="是否在校")


class StudentCreate(StudentBase):
    """创建学生Schema"""
    pass


class StudentUpdate(BaseModel):
    """更新学生Schema"""
    name: Optional[str] = Field(None, description="姓名", max_length=100)
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="电话", max_length=20)
    class_id: Optional[int] = Field(None, description="班级ID")
    major: Optional[str] = Field(None, description="专业", max_length=100)
    grade: Optional[str] = Field(None, description="年级", max_length=20)
    enrollment_year: Optional[str] = Field(None, description="入学年份", max_length=10)
    is_active: Optional[bool] = Field(None, description="是否在校")


class Student(StudentBase):
    """学生Schema"""
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class StudentWithClass(Student):
    """包含班级信息的学生Schema"""
    class_info: Optional["ClassSimple"] = None


class StudentSimple(BaseModel):
    """简化的学生Schema"""
    id: str
    name: str
    class_id: Optional[int] = None

    class Config:
        from_attributes = True


class StudentValidation(BaseModel):
    """学生验证Schema"""
    student_id: str
    exists: bool
    is_active: bool
    class_info: Optional["ClassSimple"] = None


class StudentStats(BaseModel):
    """学生统计Schema"""
    student_id: str
    name: str
    total_experiments: int = 0
    completed_experiments: int = 0
    passed_experiments: int = 0
    failed_experiments: int = 0
    average_score: Optional[float] = None
    latest_experiment_date: Optional[datetime] = None


# 避免循环导入
from app.schemas.class_schema import ClassSimple
StudentWithClass.model_rebuild()
StudentValidation.model_rebuild()
