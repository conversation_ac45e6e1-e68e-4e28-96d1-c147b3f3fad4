version: '3.8'

services:
  # MySQL数据库
  database:
    build:
      context: ./database
      dockerfile: Dockerfile
    container_name: physics-database
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: physics_root_2024
      MYSQL_DATABASE: physics_experiments
      MYSQL_USER: physics_user
      MYSQL_PASSWORD: physics_pass_2024
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_logs:/var/log/mysql
    networks:
      - physics_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pphysics_root_2024"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: physics-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - physics_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: physics-api
    restart: unless-stopped
    environment:
      - DATABASE_URL=mysql+aiomysql://physics_user:physics_pass_2024@database:3306/physics_experiments
      - REDIS_URL=redis://redis:6379/0
      - PROJECT_NAME=Physics Experiments Platform
      - API_V1_STR=/api/v1
      - SECRET_KEY=physics_secret_key_2024_change_in_production
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    volumes:
      - backend_static:/app/static
      - backend_logs:/app/logs
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - physics_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: physics-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - physics_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后台管理系统
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: physics-admin
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - physics_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: physics-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - admin
      - backend
    networks:
      - physics_network

volumes:
  mysql_data:
  mysql_logs:
  redis_data:
  backend_static:
  backend_logs:

networks:
  physics_network:
    driver: bridge
