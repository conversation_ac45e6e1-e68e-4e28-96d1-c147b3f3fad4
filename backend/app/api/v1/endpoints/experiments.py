from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
import json
import os

from app.core.database import get_db
from app.schemas.experiment import (
    ExperimentListItem, 
    ExperimentConfig, 
    ExperimentSubmission,
    ExperimentSubmissionResponse,
    ExperimentRecord,
    PlotRequest,
    PlotResponse
)
from app.services.experiment_service import ExperimentService
from app.services.plot_service import PlotService

router = APIRouter()


@router.get("/", response_model=List[ExperimentListItem])
async def get_experiment_list(
    category: Optional[str] = Query(None, description="实验分类筛选"),
    difficulty: Optional[str] = Query(None, description="难度筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取实验列表"""
    experiment_service = ExperimentService(db)
    experiments = await experiment_service.get_experiment_list(
        category=category,
        difficulty=difficulty
    )
    return experiments


@router.get("/{experiment_id}/config", response_model=ExperimentConfig)
async def get_experiment_config(
    experiment_id: str,
    db: AsyncSession = Depends(get_db)
):
    """获取实验配置"""
    experiment_service = ExperimentService(db)
    
    # 首先尝试从数据库获取
    config = await experiment_service.get_experiment_config(experiment_id)
    
    if not config:
        # 如果数据库中没有，尝试从配置文件加载
        config_path = f"app/experiments/configs/{experiment_id}.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                return ExperimentConfig(**config_data)
        else:
            raise HTTPException(status_code=404, detail="实验配置未找到")
    
    return config


@router.post("/{experiment_id}/submit", response_model=ExperimentSubmissionResponse)
async def submit_experiment(
    experiment_id: str,
    submission: ExperimentSubmission,
    db: AsyncSession = Depends(get_db)
):
    """提交实验数据"""
    experiment_service = ExperimentService(db)
    
    try:
        result = await experiment_service.submit_experiment(experiment_id, submission)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交失败: {str(e)}")


@router.get("/{experiment_id}/records")
async def get_experiment_records(
    experiment_id: str,
    student_id: Optional[str] = Query(None, description="学生ID筛选"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """获取实验记录"""
    experiment_service = ExperimentService(db)
    
    records = await experiment_service.get_experiment_records(
        experiment_id=experiment_id,
        student_id=student_id,
        page=page,
        size=size
    )
    return records


@router.get("/records/{record_id}", response_model=ExperimentRecord)
async def get_experiment_record(
    record_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取单个实验记录详情"""
    experiment_service = ExperimentService(db)
    
    record = await experiment_service.get_experiment_record_by_id(record_id)
    if not record:
        raise HTTPException(status_code=404, detail="实验记录未找到")
    
    return record


@router.post("/plot", response_model=PlotResponse)
async def generate_plot(
    plot_request: PlotRequest,
    db: AsyncSession = Depends(get_db)
):
    """生成实验图表"""
    plot_service = PlotService()
    
    try:
        result = await plot_service.generate_plot(plot_request)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图表生成失败: {str(e)}")


@router.get("/categories")
async def get_experiment_categories(
    db: AsyncSession = Depends(get_db)
):
    """获取实验分类列表"""
    experiment_service = ExperimentService(db)
    categories = await experiment_service.get_experiment_categories()
    return {"categories": categories}


@router.get("/statistics")
async def get_experiment_statistics(
    experiment_id: Optional[str] = Query(None, description="特定实验ID"),
    db: AsyncSession = Depends(get_db)
):
    """获取实验统计信息"""
    experiment_service = ExperimentService(db)
    stats = await experiment_service.get_experiment_statistics(experiment_id)
    return stats
