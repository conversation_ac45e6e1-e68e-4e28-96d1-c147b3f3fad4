from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.schemas.experiment import AnalysisRequest, AnalysisResponse
from app.services.ai_service import AIService

router = APIRouter()


@router.post("/", response_model=AnalysisResponse)
async def analyze_data(
    analysis_request: AnalysisRequest,
    db: AsyncSession = Depends(get_db)
):
    """AI数据分析"""
    ai_service = AIService(db)
    
    try:
        result = await ai_service.analyze_experiment_data(analysis_request)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@router.get("/models")
async def get_available_models(
    db: AsyncSession = Depends(get_db)
):
    """获取可用的AI模型列表"""
    ai_service = AIService(db)
    models = await ai_service.get_available_models()
    return {"models": models}
