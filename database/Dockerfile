# 使用官方MySQL镜像
FROM mysql:8.0

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=physics_root_2024
ENV MYSQL_DATABASE=physics_experiments
ENV MYSQL_USER=physics_user
ENV MYSQL_PASSWORD=physics_pass_2024

# 复制初始化脚本
COPY ./init/ /docker-entrypoint-initdb.d/

# 复制配置文件
COPY ./config/my.cnf /etc/mysql/conf.d/

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 暴露端口
EXPOSE 3306

# 设置数据目录权限
RUN chown -R mysql:mysql /var/lib/mysql

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD mysqladmin ping -h localhost -u root -p$MYSQL_ROOT_PASSWORD || exit 1
