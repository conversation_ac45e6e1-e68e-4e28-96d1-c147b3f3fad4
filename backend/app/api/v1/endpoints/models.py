from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.core.database import get_db
from app.schemas.experiment import AIModel, AIModelCreate, AIModelUpdate, AIModelPublic
from app.services.ai_service import AIService

router = APIRouter()


@router.get("/", response_model=List[AIModelPublic])
async def get_models(
    db: AsyncSession = Depends(get_db)
):
    """获取AI模型列表（公开信息）"""
    ai_service = AIService(db)
    models = await ai_service.get_available_models()
    return models


@router.post("/", response_model=AIModel)
async def create_model(
    model: AIModelCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建AI模型配置"""
    ai_service = AIService(db)
    return await ai_service.create_model(model)


@router.get("/{model_id}", response_model=AIModel)
async def get_model(
    model_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取AI模型详情"""
    ai_service = AIService(db)
    model = await ai_service.get_model(model_id)
    if not model:
        raise HTTPException(status_code=404, detail="模型不存在")
    return model


@router.put("/{model_id}", response_model=AIModel)
async def update_model(
    model_id: int,
    model_update: AIModelUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新AI模型配置"""
    ai_service = AIService(db)
    model = await ai_service.update_model(model_id, model_update)
    if not model:
        raise HTTPException(status_code=404, detail="模型不存在")
    return model


@router.delete("/{model_id}")
async def delete_model(
    model_id: int,
    db: AsyncSession = Depends(get_db)
):
    """删除AI模型配置"""
    ai_service = AIService(db)
    success = await ai_service.delete_model(model_id)
    if not success:
        raise HTTPException(status_code=404, detail="模型不存在")
    return {"message": "模型删除成功"}
