from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.models.experiment import Class, Student, ExperimentRecord
from app.schemas.class_schema import ClassCreate, ClassUpdate
from app.schemas.student_schema import StudentStats


class ClassService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_class(self, class_data: ClassCreate) -> Class:
        """创建班级"""
        db_class = Class(**class_data.model_dump())
        self.db.add(db_class)
        await self.db.commit()
        await self.db.refresh(db_class)
        return db_class

    async def get_class(self, class_id: int) -> Optional[Class]:
        """获取班级信息"""
        result = await self.db.execute(
            select(Class).where(Class.id == class_id)
        )
        return result.scalar_one_or_none()

    async def get_class_by_code(self, class_code: str) -> Optional[Class]:
        """根据班级代码获取班级"""
        result = await self.db.execute(
            select(Class).where(Class.class_code == class_code)
        )
        return result.scalar_one_or_none()

    async def get_class_with_students(self, class_id: int) -> Optional[Class]:
        """获取包含学生信息的班级"""
        result = await self.db.execute(
            select(Class)
            .options(selectinload(Class.students))
            .where(Class.id == class_id)
        )
        return result.scalar_one_or_none()

    async def get_classes(
        self,
        page: int = 1,
        size: int = 20,
        search: Optional[str] = None,
        grade: Optional[str] = None,
        major: Optional[str] = None,
        academic_year: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Class]:
        """获取班级列表"""
        query = select(Class)
        
        # 构建筛选条件
        conditions = []
        
        if search:
            search_condition = or_(
                Class.class_name.ilike(f"%{search}%"),
                Class.class_code.ilike(f"%{search}%"),
                Class.major.ilike(f"%{search}%"),
                Class.teacher_name.ilike(f"%{search}%")
            )
            conditions.append(search_condition)
        
        if grade:
            conditions.append(Class.grade == grade)
        
        if major:
            conditions.append(Class.major.ilike(f"%{major}%"))
        
        if academic_year:
            conditions.append(Class.academic_year == academic_year)
        
        if is_active is not None:
            conditions.append(Class.is_active == is_active)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 分页
        query = query.offset((page - 1) * size).limit(size)
        query = query.order_by(Class.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()

    async def update_class(self, class_id: int, class_update: ClassUpdate) -> Optional[Class]:
        """更新班级信息"""
        result = await self.db.execute(
            select(Class).where(Class.id == class_id)
        )
        db_class = result.scalar_one_or_none()
        
        if not db_class:
            return None
        
        # 更新字段
        update_data = class_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_class, field, value)
        
        await self.db.commit()
        await self.db.refresh(db_class)
        return db_class

    async def delete_class(self, class_id: int, force: bool = False) -> bool:
        """删除班级"""
        result = await self.db.execute(
            select(Class).where(Class.id == class_id)
        )
        db_class = result.scalar_one_or_none()
        
        if not db_class:
            return False
        
        if force:
            # 强制删除：先将所有学生的班级ID设为None
            await self.db.execute(
                select(Student)
                .where(Student.class_id == class_id)
                .update({Student.class_id: None})
            )
        
        await self.db.delete(db_class)
        await self.db.commit()
        return True

    async def add_student_to_class(self, class_id: int, student_id: str) -> bool:
        """将学生添加到班级"""
        # 检查班级是否存在
        class_result = await self.db.execute(
            select(Class).where(Class.id == class_id)
        )
        db_class = class_result.scalar_one_or_none()
        if not db_class:
            return False
        
        # 检查学生是否存在
        student_result = await self.db.execute(
            select(Student).where(Student.id == student_id)
        )
        db_student = student_result.scalar_one_or_none()
        if not db_student:
            return False
        
        # 更新学生的班级ID
        db_student.class_id = class_id
        
        # 更新班级学生数量
        await self._update_class_student_count(class_id)
        
        await self.db.commit()
        return True

    async def remove_student_from_class(self, class_id: int, student_id: str) -> bool:
        """从班级中移除学生"""
        result = await self.db.execute(
            select(Student).where(
                and_(Student.id == student_id, Student.class_id == class_id)
            )
        )
        db_student = result.scalar_one_or_none()
        
        if not db_student:
            return False
        
        # 移除学生的班级关联
        db_student.class_id = None
        
        # 更新班级学生数量
        await self._update_class_student_count(class_id)
        
        await self.db.commit()
        return True

    async def get_class_student_stats(self, class_id: int) -> List[StudentStats]:
        """获取班级学生统计信息"""
        # 获取班级所有学生的实验统计
        query = """
        SELECT 
            s.id as student_id,
            s.name,
            COUNT(er.id) as total_experiments,
            COUNT(CASE WHEN er.status = 'approved' THEN 1 END) as completed_experiments,
            COUNT(CASE WHEN er.pass_status = 'passed' THEN 1 END) as passed_experiments,
            COUNT(CASE WHEN er.pass_status = 'failed' THEN 1 END) as failed_experiments,
            AVG(er.score) as average_score,
            MAX(er.submit_time) as latest_experiment_date
        FROM students s
        LEFT JOIN experiment_records er ON s.id = er.student_id
        WHERE s.class_id = :class_id
        GROUP BY s.id, s.name
        ORDER BY s.name
        """
        
        result = await self.db.execute(query, {"class_id": class_id})
        rows = result.fetchall()
        
        stats = []
        for row in rows:
            stats.append(StudentStats(
                student_id=row.student_id,
                name=row.name,
                total_experiments=row.total_experiments or 0,
                completed_experiments=row.completed_experiments or 0,
                passed_experiments=row.passed_experiments or 0,
                failed_experiments=row.failed_experiments or 0,
                average_score=row.average_score,
                latest_experiment_date=row.latest_experiment_date
            ))
        
        return stats

    async def _update_class_student_count(self, class_id: int):
        """更新班级学生数量"""
        count_result = await self.db.execute(
            select(func.count(Student.id)).where(Student.class_id == class_id)
        )
        student_count = count_result.scalar()
        
        class_result = await self.db.execute(
            select(Class).where(Class.id == class_id)
        )
        db_class = class_result.scalar_one_or_none()
        
        if db_class:
            db_class.student_count = student_count
