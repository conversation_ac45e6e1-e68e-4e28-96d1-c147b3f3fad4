from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Float, <PERSON>olean, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any
import enum

from app.core.database import Base


class PassStatus(enum.Enum):
    """通过状态枚举"""
    PENDING = "pending"      # 待评判
    PASSED = "passed"        # 通过
    FAILED = "failed"        # 未通过
    REVIEWING = "reviewing"  # 复审中


class Class(Base):
    """班级模型"""
    __tablename__ = "classes"

    id = Column(Integer, primary_key=True, index=True)
    class_code = Column(String(50), unique=True, nullable=False, index=True)  # 班级代码
    class_name = Column(String(100), nullable=False)  # 班级名称
    grade = Column(String(20), nullable=False)  # 年级
    major = Column(String(100), nullable=False)  # 专业
    department = Column(String(100), nullable=True)  # 院系
    teacher_name = Column(String(100), nullable=True)  # 任课教师
    teacher_email = Column(String(255), nullable=True)  # 教师邮箱
    academic_year = Column(String(20), nullable=False)  # 学年
    semester = Column(String(20), nullable=False)  # 学期
    student_count = Column(Integer, default=0)  # 学生人数
    is_active = Column(Boolean, default=True)  # 是否启用
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    students = relationship("Student", back_populates="class_info")


class Student(Base):
    """学生模型"""
    __tablename__ = "students"

    id = Column(String(50), primary_key=True, index=True)  # 学号
    name = Column(String(100), nullable=False)  # 姓名
    email = Column(String(255), nullable=True)  # 邮箱
    phone = Column(String(20), nullable=True)  # 电话
    class_id = Column(Integer, ForeignKey("classes.id"), nullable=True)  # 班级ID
    major = Column(String(100), nullable=True)  # 专业
    grade = Column(String(20), nullable=True)  # 年级
    enrollment_year = Column(String(10), nullable=True)  # 入学年份
    is_active = Column(Boolean, default=True)  # 是否在校
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    class_info = relationship("Class", back_populates="students")
    experiments = relationship("ExperimentRecord", back_populates="student")
    ai_analyses = relationship("AIAnalysis", back_populates="student")


class ExperimentRecord(Base):
    """实验记录模型"""
    __tablename__ = "experiment_records"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(50), ForeignKey("students.id"), nullable=False)
    experiment_type = Column(String(100), nullable=False, index=True)  # 实验类型
    experiment_name = Column(String(200), nullable=False)  # 实验名称
    version = Column(Integer, default=1)  # 版本号
    status = Column(String(50), default="submitted")  # 状态: submitted, reviewed, approved

    # 实验数据
    raw_data = Column(JSON)  # 原始数据
    processed_data = Column(JSON)  # 处理后的数据
    analysis_result = Column(JSON)  # 分析结果
    charts_data = Column(JSON)  # 图表数据

    # 评分相关
    score = Column(Float, nullable=True)  # 分数
    max_score = Column(Float, default=100.0)  # 满分
    pass_threshold = Column(Float, default=60.0)  # 及格线
    pass_status = Column(Enum(PassStatus), default=PassStatus.PENDING)  # 通过状态
    feedback = Column(Text, nullable=True)  # 反馈
    reviewer_id = Column(String(50), nullable=True)  # 评阅者ID
    reviewed_at = Column(DateTime(timezone=True), nullable=True)  # 评阅时间

    # AI分析相关
    ai_analysis_id = Column(Integer, ForeignKey("ai_analyses.id"), nullable=True)  # AI分析ID
    auto_score = Column(Float, nullable=True)  # AI自动评分
    manual_override = Column(Boolean, default=False)  # 是否人工覆盖

    # 时间戳
    submit_time = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    student = relationship("Student", back_populates="experiments")
    ai_analysis = relationship("AIAnalysis", back_populates="experiment_record")


class AIModel(Base):
    """AI模型配置"""
    __tablename__ = "ai_models"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)  # 模型显示名称
    internal_name = Column(String(100), nullable=False)  # 模型内部名称
    provider = Column(String(50), nullable=False)  # 提供商: openai, anthropic, etc.
    api_key = Column(String(500), nullable=False)  # API密钥
    base_url = Column(String(500), nullable=True)  # API基础URL
    max_tokens = Column(Integer, default=4000)  # 最大token数
    temperature = Column(Float, default=0.7)  # 温度参数
    is_active = Column(Boolean, default=True)  # 是否启用
    is_default = Column(Boolean, default=False)  # 是否为默认模型
    description = Column(Text, nullable=True)  # 描述
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class AIAnalysis(Base):
    """AI分析结果模型"""
    __tablename__ = "ai_analyses"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(50), ForeignKey("students.id"), nullable=False)
    experiment_type = Column(String(100), nullable=False)  # 实验类型
    analysis_type = Column(String(50), default="auto")  # 分析类型: auto, manual, hybrid

    # 分析结果
    analysis_data = Column(JSON, nullable=False)  # 详细分析数据
    conclusions = Column(JSON)  # 结论列表
    suggestions = Column(JSON)  # 改进建议
    error_analysis = Column(JSON)  # 错误分析

    # 评分详情
    scoring_details = Column(JSON)  # 评分详情
    confidence_score = Column(Float, nullable=True)  # 置信度分数
    quality_metrics = Column(JSON)  # 质量指标

    # AI模型信息
    model_name = Column(String(100), nullable=True)  # 使用的AI模型
    model_version = Column(String(50), nullable=True)  # 模型版本
    processing_time = Column(Float, nullable=True)  # 处理时间(秒)

    # 状态
    status = Column(String(50), default="completed")  # 状态: processing, completed, failed
    error_message = Column(Text, nullable=True)  # 错误信息

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    student = relationship("Student", back_populates="ai_analyses")
    experiment_record = relationship("ExperimentRecord", back_populates="ai_analysis")


class ExperimentTemplate(Base):
    """实验模板"""
    __tablename__ = "experiment_templates"

    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(String(100), unique=True, nullable=False, index=True)  # 实验ID
    name = Column(String(200), nullable=False)  # 实验名称
    description = Column(Text, nullable=True)  # 实验描述
    category = Column(String(100), nullable=False)  # 实验分类
    difficulty = Column(String(20), default="medium")  # 难度: easy, medium, hard
    duration = Column(Integer, default=60)  # 预计时长(分钟)
    tags = Column(JSON)  # 标签列表

    # 实验配置
    config = Column(JSON, nullable=False)  # 实验配置JSON
    steps_config = Column(JSON, nullable=False)  # 步骤配置

    # 评分配置
    scoring_config = Column(JSON)  # 评分配置
    pass_criteria = Column(JSON)  # 通过标准

    # 状态
    is_active = Column(Boolean, default=True)  # 是否启用
    version = Column(String(20), default="1.0.0")  # 版本号
    
    # 统计信息
    total_submissions = Column(Integer, default=0)  # 总提交数
    average_score = Column(Float, nullable=True)  # 平均分数
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class SystemLog(Base):
    """系统日志"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(20), nullable=False)  # 日志级别: DEBUG, INFO, WARNING, ERROR
    message = Column(Text, nullable=False)  # 日志消息
    module = Column(String(100), nullable=True)  # 模块名
    function = Column(String(100), nullable=True)  # 函数名
    user_id = Column(String(50), nullable=True)  # 用户ID
    ip_address = Column(String(45), nullable=True)  # IP地址
    user_agent = Column(String(500), nullable=True)  # 用户代理
    extra_data = Column(JSON, nullable=True)  # 额外数据
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class FileUpload(Base):
    """文件上传记录"""
    __tablename__ = "file_uploads"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)  # 原始文件名
    stored_filename = Column(String(255), nullable=False)  # 存储文件名
    file_path = Column(String(500), nullable=False)  # 文件路径
    file_size = Column(Integer, nullable=False)  # 文件大小(字节)
    mime_type = Column(String(100), nullable=True)  # MIME类型
    uploader_id = Column(String(50), nullable=True)  # 上传者ID
    experiment_id = Column(String(100), nullable=True)  # 关联实验ID
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
