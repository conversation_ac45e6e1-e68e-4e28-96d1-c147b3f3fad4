import request from './index'

export interface DashboardStats {
  totalStudents: number
  totalClasses: number
  totalExperiments: number
  totalRecords: number
  todaySubmissions: number
  pendingReviews: number
  passRate: number
  averageScore: number
}

export interface ActivityRecord {
  id: number
  time: string
  studentId: string
  studentName: string
  experimentType: string
  experimentName: string
  action: string
  status: string
}

export interface ChartData {
  submissionTrend: {
    dates: string[]
    counts: number[]
  }
  gradeDistribution: {
    ranges: string[]
    counts: number[]
  }
  passRateData: {
    passed: number
    failed: number
  }
}

export const dashboardApi = {
  // 获取仪表盘统计数据
  getStats(): Promise<DashboardStats> {
    return request({
      url: '/dashboard/stats',
      method: 'get'
    })
  },

  // 获取最近活动记录
  getRecentActivities(limit = 10): Promise<ActivityRecord[]> {
    return request({
      url: '/dashboard/activities',
      method: 'get',
      params: { limit }
    })
  },

  // 获取图表数据
  getChartData(days = 7): Promise<ChartData> {
    return request({
      url: '/dashboard/charts',
      method: 'get',
      params: { days }
    })
  },

  // 获取实验提交趋势
  getSubmissionTrend(days = 7): Promise<{ dates: string[], counts: number[] }> {
    return request({
      url: '/dashboard/submission-trend',
      method: 'get',
      params: { days }
    })
  },

  // 获取成绩分布
  getGradeDistribution(): Promise<{ ranges: string[], counts: number[] }> {
    return request({
      url: '/dashboard/grade-distribution',
      method: 'get'
    })
  },

  // 获取通过率统计
  getPassRateStats(): Promise<{ passed: number, failed: number, total: number }> {
    return request({
      url: '/dashboard/pass-rate',
      method: 'get'
    })
  }
}
