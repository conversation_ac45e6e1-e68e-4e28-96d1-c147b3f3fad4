<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon student">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalStudents }}</div>
              <div class="stat-label">学生总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon class">
              <el-icon><School /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalClasses }}</div>
              <div class="stat-label">班级总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon experiment">
              <el-icon><Experiment /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalExperiments }}</div>
              <div class="stat-label">实验总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon record">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalRecords }}</div>
              <div class="stat-label">实验记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card title="实验提交趋势">
          <div ref="submissionTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="成绩分布">
          <div ref="gradeDistributionChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="8">
        <el-card title="实验通过率">
          <div ref="passRateChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-card title="最近活动">
          <el-table :data="recentActivities" style="width: 100%">
            <el-table-column prop="time" label="时间" width="120" />
            <el-table-column prop="student" label="学生" width="100" />
            <el-table-column prop="experiment" label="实验" width="150" />
            <el-table-column prop="action" label="操作" width="100" />
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { User, School, Experiment, Document } from '@element-plus/icons-vue'
import { dashboardApi } from '@/api/dashboard'

const stats = ref({
  totalStudents: 0,
  totalClasses: 0,
  totalExperiments: 0,
  totalRecords: 0
})

const recentActivities = ref([
  {
    time: '10:30',
    student: '张三',
    experiment: '单摆实验',
    action: '提交',
    status: '已完成'
  },
  {
    time: '10:25',
    student: '李四',
    experiment: '光学干涉',
    action: '提交',
    status: '待审核'
  },
  {
    time: '10:20',
    student: '王五',
    experiment: '单摆实验',
    action: '提交',
    status: '已通过'
  }
])

const submissionTrendChart = ref()
const gradeDistributionChart = ref()
const passRateChart = ref()

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已完成': 'success',
    '待审核': 'warning',
    '已通过': 'success',
    '未通过': 'danger'
  }
  return statusMap[status] || 'info'
}

const initCharts = () => {
  nextTick(() => {
    // 实验提交趋势图
    const submissionChart = echarts.init(submissionTrendChart.value)
    submissionChart.setOption({
      title: {
        text: '最近7天实验提交趋势'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [12, 19, 15, 22, 18, 8, 5],
        type: 'line',
        smooth: true,
        areaStyle: {}
      }]
    })

    // 成绩分布图
    const gradeChart = echarts.init(gradeDistributionChart.value)
    gradeChart.setOption({
      title: {
        text: '成绩分布'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: ['0-59', '60-69', '70-79', '80-89', '90-100']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [5, 12, 25, 35, 23],
        type: 'bar',
        itemStyle: {
          color: '#409EFF'
        }
      }]
    })

    // 通过率饼图
    const passChart = echarts.init(passRateChart.value)
    passChart.setOption({
      title: {
        text: '实验通过率',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: '50%',
        data: [
          { value: 85, name: '通过' },
          { value: 15, name: '未通过' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    })
  })
}

const loadDashboardData = async () => {
  try {
    const data = await dashboardApi.getStats()
    stats.value = data
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
  initCharts()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          .el-icon {
            font-size: 24px;
            color: white;
          }
          
          &.student {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.class {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.experiment {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.record {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .stat-info {
          .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
    
    .chart-container {
      height: 300px;
    }
  }
}
</style>
