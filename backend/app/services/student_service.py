from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from typing import List, Optional, Dict, Any

from app.models.experiment import Student, ExperimentRecord
from app.schemas.experiment import (
    StudentCreate,
    StudentUpdate,
    Student as StudentSchema,
    StudentValidation,
    ExperimentRecord as ExperimentRecordSchema
)


class StudentService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_student(self, student_data: StudentCreate) -> StudentSchema:
        """创建学生"""
        student = Student(**student_data.model_dump())
        self.db.add(student)
        await self.db.commit()
        await self.db.refresh(student)
        return StudentSchema.model_validate(student)

    async def get_student(self, student_id: str) -> Optional[StudentSchema]:
        """获取学生信息"""
        query = select(Student).where(Student.id == student_id)
        result = await self.db.execute(query)
        student = result.scalar_one_or_none()
        
        if student:
            return StudentSchema.model_validate(student)
        return None

    async def update_student(self, student_id: str, student_update: StudentUpdate) -> Optional[StudentSchema]:
        """更新学生信息"""
        query = select(Student).where(Student.id == student_id)
        result = await self.db.execute(query)
        student = result.scalar_one_or_none()
        
        if not student:
            return None
        
        # 更新字段
        update_data = student_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(student, field, value)
        
        await self.db.commit()
        await self.db.refresh(student)
        return StudentSchema.model_validate(student)

    async def validate_student(self, student_id: str) -> StudentValidation:
        """验证学生信息"""
        query = select(Student).where(Student.id == student_id)
        result = await self.db.execute(query)
        student = result.scalar_one_or_none()
        
        if student:
            return StudentValidation(exists=True, name=student.name)
        else:
            return StudentValidation(exists=False)

    async def get_student_records(
        self,
        student_id: str,
        experiment_id: Optional[str] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """获取学生实验记录"""
        query = select(ExperimentRecord).where(ExperimentRecord.student_id == student_id)
        
        if experiment_id:
            query = query.where(ExperimentRecord.experiment_type == experiment_id)
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.order_by(ExperimentRecord.submit_time.desc())
        query = query.offset((page - 1) * size).limit(size)
        
        result = await self.db.execute(query)
        records = result.scalars().all()
        
        return {
            "items": [ExperimentRecordSchema.model_validate(record) for record in records],
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }

    async def get_students(
        self,
        page: int = 1,
        size: int = 20,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取学生列表"""
        query = select(Student)
        
        if search:
            query = query.where(
                or_(
                    Student.id.contains(search),
                    Student.name.contains(search),
                    Student.class_name.contains(search)
                )
            )
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.order_by(Student.created_at.desc())
        query = query.offset((page - 1) * size).limit(size)
        
        result = await self.db.execute(query)
        students = result.scalars().all()
        
        return {
            "items": [StudentSchema.model_validate(student) for student in students],
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
