from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class ClassBase(BaseModel):
    """班级基础Schema"""
    class_code: str = Field(..., description="班级代码", max_length=50)
    class_name: str = Field(..., description="班级名称", max_length=100)
    grade: str = Field(..., description="年级", max_length=20)
    major: str = Field(..., description="专业", max_length=100)
    department: Optional[str] = Field(None, description="院系", max_length=100)
    teacher_name: Optional[str] = Field(None, description="任课教师", max_length=100)
    teacher_email: Optional[str] = Field(None, description="教师邮箱", max_length=255)
    academic_year: str = Field(..., description="学年", max_length=20)
    semester: str = Field(..., description="学期", max_length=20)
    is_active: bool = Field(True, description="是否启用")


class ClassCreate(ClassBase):
    """创建班级Schema"""
    pass


class ClassUpdate(BaseModel):
    """更新班级Schema"""
    class_name: Optional[str] = Field(None, description="班级名称", max_length=100)
    grade: Optional[str] = Field(None, description="年级", max_length=20)
    major: Optional[str] = Field(None, description="专业", max_length=100)
    department: Optional[str] = Field(None, description="院系", max_length=100)
    teacher_name: Optional[str] = Field(None, description="任课教师", max_length=100)
    teacher_email: Optional[str] = Field(None, description="教师邮箱", max_length=255)
    academic_year: Optional[str] = Field(None, description="学年", max_length=20)
    semester: Optional[str] = Field(None, description="学期", max_length=20)
    is_active: Optional[bool] = Field(None, description="是否启用")


class Class(ClassBase):
    """班级Schema"""
    id: int
    student_count: int = Field(0, description="学生人数")
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ClassWithStudents(Class):
    """包含学生信息的班级Schema"""
    students: List["StudentSimple"] = []


class ClassSimple(BaseModel):
    """简化的班级Schema"""
    id: int
    class_code: str
    class_name: str
    grade: str
    major: str

    class Config:
        from_attributes = True


# 避免循环导入
from app.schemas.student_schema import StudentSimple
ClassWithStudents.model_rebuild()
