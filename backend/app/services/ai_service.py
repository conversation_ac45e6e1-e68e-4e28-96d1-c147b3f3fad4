from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import List, Optional, Dict, Any
import json

from app.models.experiment import AIModel
from app.schemas.experiment import (
    AIModelCreate,
    AIModelUpdate,
    AIModel as AIModelSchema,
    AIModelPublic,
    AnalysisRequest,
    AnalysisResponse
)


class AIService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_available_models(self) -> List[AIModelPublic]:
        """获取可用的AI模型列表"""
        query = select(AIModel).where(AIModel.is_active == True)
        result = await self.db.execute(query)
        models = result.scalars().all()
        
        return [
            AIModelPublic(
                id=model.id,
                name=model.name,
                provider=model.provider,
                is_active=model.is_active,
                is_default=model.is_default,
                description=model.description
            )
            for model in models
        ]

    async def create_model(self, model_data: AIModelCreate) -> AIModelSchema:
        """创建AI模型配置"""
        model = AIModel(**model_data.model_dump())
        self.db.add(model)
        await self.db.commit()
        await self.db.refresh(model)
        return AIModelSchema.model_validate(model)

    async def get_model(self, model_id: int) -> Optional[AIModelSchema]:
        """获取AI模型详情"""
        query = select(AIModel).where(AIModel.id == model_id)
        result = await self.db.execute(query)
        model = result.scalar_one_or_none()
        
        if model:
            return AIModelSchema.model_validate(model)
        return None

    async def update_model(self, model_id: int, model_update: AIModelUpdate) -> Optional[AIModelSchema]:
        """更新AI模型配置"""
        query = select(AIModel).where(AIModel.id == model_id)
        result = await self.db.execute(query)
        model = result.scalar_one_or_none()
        
        if not model:
            return None
        
        # 更新字段
        update_data = model_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(model, field, value)
        
        await self.db.commit()
        await self.db.refresh(model)
        return AIModelSchema.model_validate(model)

    async def delete_model(self, model_id: int) -> bool:
        """删除AI模型配置"""
        query = select(AIModel).where(AIModel.id == model_id)
        result = await self.db.execute(query)
        model = result.scalar_one_or_none()
        
        if not model:
            return False
        
        await self.db.delete(model)
        await self.db.commit()
        return True

    async def analyze_experiment_data(self, analysis_request: AnalysisRequest) -> AnalysisResponse:
        """AI数据分析"""
        try:
            # 获取指定的AI模型或默认模型
            model = None
            if analysis_request.model_id:
                model = await self.get_model(int(analysis_request.model_id))
            
            if not model:
                # 获取默认模型
                query = select(AIModel).where(
                    and_(AIModel.is_active == True, AIModel.is_default == True)
                )
                result = await self.db.execute(query)
                model_record = result.scalar_one_or_none()
                if model_record:
                    model = AIModelSchema.model_validate(model_record)
            
            if not model:
                return AnalysisResponse(
                    success=False,
                    message="没有可用的AI模型"
                )
            
            # 这里应该调用实际的AI服务
            # 由于没有配置真实的AI API，返回模拟结果
            analysis_text = self._generate_mock_analysis(analysis_request)
            
            return AnalysisResponse(
                success=True,
                text=analysis_text,
                conclusion="基于数据分析，实验结果符合理论预期。",
                suggestions=[
                    "建议增加测量次数以提高数据可靠性",
                    "注意环境因素对测量结果的影响",
                    "可以尝试不同的实验条件进行对比"
                ],
                model_used=model.name
            )
            
        except Exception as e:
            return AnalysisResponse(
                success=False,
                message=f"分析过程中出现错误: {str(e)}"
            )

    def _generate_mock_analysis(self, analysis_request: AnalysisRequest) -> str:
        """生成模拟的分析结果"""
        experiment_type = analysis_request.experiment_type
        
        if experiment_type == "oscilloscope":
            return """
            **示波器实验数据分析**
            
            根据提供的实验数据，我对您的示波器实验进行了详细分析：
            
            1. **频率测量精度**: 测量频率与设定频率的偏差在合理范围内，说明示波器的频率测量功能工作正常。
            
            2. **波形质量**: 从测量的峰峰值电压可以看出，信号质量良好，没有明显的失真。
            
            3. **测量一致性**: 频率和周期的测量结果相互验证，显示了良好的测量一致性。
            
            **改进建议**:
            - 可以增加更多频率点的测量以获得更全面的特性曲线
            - 建议记录环境温度，因为温度可能影响电子器件的性能
            - 可以尝试不同波形类型的测量对比
            """
        else:
            return f"""
            **{experiment_type}实验数据分析**
            
            基于您提供的实验数据，分析结果如下：
            
            1. **数据质量**: 实验数据完整，测量方法正确。
            
            2. **结果评估**: 测量结果在预期范围内，符合理论预期。
            
            3. **误差分析**: 系统误差和随机误差都在可接受范围内。
            
            总体而言，本次实验完成质量良好，达到了预期的学习目标。
            """
