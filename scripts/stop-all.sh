#!/bin/bash

# 物理实验平台停止脚本
# 用于停止所有服务容器

set -e

echo "🛑 停止物理实验平台..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装"
    exit 1
fi

# 显示当前运行的容器
echo "📋 当前运行的容器："
docker-compose ps

# 停止所有服务
echo "🛑 停止所有服务..."
docker-compose down

# 询问是否删除数据卷
read -p "是否删除数据卷（将清除所有数据）？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除数据卷..."
    docker-compose down -v
    echo "⚠️  所有数据已被删除"
fi

# 询问是否删除镜像
read -p "是否删除构建的镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除镜像..."
    docker-compose down --rmi all
fi

# 清理未使用的资源
read -p "是否清理未使用的Docker资源？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理Docker资源..."
    docker system prune -f
fi

echo ""
echo "✅ 物理实验平台已停止"
echo ""
echo "🚀 重新启动："
echo "   ./scripts/start-all.sh"
echo ""
