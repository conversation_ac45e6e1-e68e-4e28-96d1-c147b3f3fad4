from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional
import os

from app.models.experiment import FileUpload
from app.schemas.experiment import MessageResponse


class FileService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_file_record(
        self,
        filename: str,
        stored_filename: str,
        file_path: str,
        file_size: int,
        mime_type: Optional[str] = None,
        uploader_id: Optional[str] = None,
        experiment_id: Optional[str] = None
    ) -> FileUpload:
        """创建文件记录"""
        file_record = FileUpload(
            filename=filename,
            stored_filename=stored_filename,
            file_path=file_path,
            file_size=file_size,
            mime_type=mime_type,
            uploader_id=uploader_id,
            experiment_id=experiment_id
        )
        
        self.db.add(file_record)
        await self.db.commit()
        await self.db.refresh(file_record)
        return file_record

    async def get_file_info(self, file_id: int) -> Optional[dict]:
        """获取文件信息"""
        query = select(FileUpload).where(FileUpload.id == file_id)
        result = await self.db.execute(query)
        file_record = result.scalar_one_or_none()
        
        if not file_record:
            return None
        
        return {
            "id": file_record.id,
            "filename": file_record.filename,
            "stored_filename": file_record.stored_filename,
            "file_size": file_record.file_size,
            "mime_type": file_record.mime_type,
            "uploader_id": file_record.uploader_id,
            "experiment_id": file_record.experiment_id,
            "created_at": file_record.created_at,
            "url": f"/static/uploads/{file_record.stored_filename}"
        }

    async def delete_file(self, file_id: int) -> bool:
        """删除文件"""
        query = select(FileUpload).where(FileUpload.id == file_id)
        result = await self.db.execute(query)
        file_record = result.scalar_one_or_none()
        
        if not file_record:
            return False
        
        # 删除物理文件
        if os.path.exists(file_record.file_path):
            try:
                os.remove(file_record.file_path)
            except OSError:
                pass  # 文件删除失败，但继续删除数据库记录
        
        # 删除数据库记录
        await self.db.delete(file_record)
        await self.db.commit()
        return True
