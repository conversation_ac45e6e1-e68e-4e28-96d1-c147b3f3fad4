from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from app.core.database import get_db
from app.schemas.experiment import (
    Student,
    StudentCreate,
    StudentUpdate,
    StudentValidation,
    ExperimentRecord,
    PaginatedResponse
)
from app.services.student_service import StudentService

router = APIRouter()


@router.post("/", response_model=Student)
async def create_student(
    student: StudentCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建学生"""
    student_service = StudentService(db)
    return await student_service.create_student(student)


@router.get("/{student_id}", response_model=Student)
async def get_student(
    student_id: str,
    db: AsyncSession = Depends(get_db)
):
    """获取学生信息"""
    student_service = StudentService(db)
    student = await student_service.get_student(student_id)
    if not student:
        raise HTTPException(status_code=404, detail="学生不存在")
    return student


@router.put("/{student_id}", response_model=Student)
async def update_student(
    student_id: str,
    student_update: StudentUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新学生信息"""
    student_service = StudentService(db)
    student = await student_service.update_student(student_id, student_update)
    if not student:
        raise HTTPException(status_code=404, detail="学生不存在")
    return student


@router.get("/{student_id}/validate", response_model=StudentValidation)
async def validate_student(
    student_id: str,
    db: AsyncSession = Depends(get_db)
):
    """验证学生信息"""
    student_service = StudentService(db)
    return await student_service.validate_student(student_id)


@router.get("/{student_id}/records")
async def get_student_records(
    student_id: str,
    experiment_id: Optional[str] = Query(None, description="实验ID筛选"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """获取学生实验记录"""
    student_service = StudentService(db)
    return await student_service.get_student_records(
        student_id=student_id,
        experiment_id=experiment_id,
        page=page,
        size=size
    )


@router.get("/")
async def get_students(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db)
):
    """获取学生列表"""
    student_service = StudentService(db)
    return await student_service.get_students(
        page=page,
        size=size,
        search=search
    )
