from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, update
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime
import csv
import io
import os

from app.models.experiment import ExperimentRecord, Student, Class, ExperimentTemplate, PassStatus, AIAnalysis
from app.schemas.grade_schema import (
    GradeUpdate, 
    PassStatusUpdate, 
    GradeResponse, 
    GradeStatistics,
    ClassGradesSummary,
    BatchGradeResult,
    PendingReviewRecord,
    ExperimentGradeStatistics
)
from app.core.config import settings


class GradeService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def update_experiment_score(self, record_id: int, grade_update: GradeUpdate) -> Optional[GradeResponse]:
        """更新实验记录分数"""
        result = await self.db.execute(
            select(ExperimentRecord).where(ExperimentRecord.id == record_id)
        )
        record = result.scalar_one_or_none()
        
        if not record:
            return None
        
        # 更新分数相关字段
        if grade_update.score is not None:
            record.score = grade_update.score
        if grade_update.max_score is not None:
            record.max_score = grade_update.max_score
        if grade_update.pass_threshold is not None:
            record.pass_threshold = grade_update.pass_threshold
        if grade_update.feedback is not None:
            record.feedback = grade_update.feedback
        if grade_update.reviewer_id is not None:
            record.reviewer_id = grade_update.reviewer_id
        if grade_update.manual_override is not None:
            record.manual_override = grade_update.manual_override
        
        # 自动判定通过状态
        if record.score is not None:
            if record.score >= record.pass_threshold:
                record.pass_status = PassStatus.PASSED
            else:
                record.pass_status = PassStatus.FAILED
        
        record.reviewed_at = datetime.now()
        record.status = "reviewed"
        
        await self.db.commit()
        await self.db.refresh(record)
        
        return GradeResponse.model_validate(record)

    async def update_pass_status(self, record_id: int, status_update: PassStatusUpdate) -> Optional[GradeResponse]:
        """更新通过状态"""
        result = await self.db.execute(
            select(ExperimentRecord).where(ExperimentRecord.id == record_id)
        )
        record = result.scalar_one_or_none()
        
        if not record:
            return None
        
        record.pass_status = status_update.pass_status
        if status_update.feedback:
            record.feedback = status_update.feedback
        if status_update.reviewer_id:
            record.reviewer_id = status_update.reviewer_id
        
        record.reviewed_at = datetime.now()
        record.status = "reviewed"
        
        await self.db.commit()
        await self.db.refresh(record)
        
        return GradeResponse.model_validate(record)

    async def auto_grade_experiment(self, record_id: int, use_ai_score: bool = True) -> Optional[GradeResponse]:
        """自动评分实验"""
        result = await self.db.execute(
            select(ExperimentRecord)
            .options(selectinload(ExperimentRecord.ai_analysis))
            .where(ExperimentRecord.id == record_id)
        )
        record = result.scalar_one_or_none()
        
        if not record:
            return None
        
        # 使用AI分数或默认评分逻辑
        if use_ai_score and record.ai_analysis and record.auto_score:
            score = record.auto_score
        else:
            # 基于数据质量的简单评分逻辑
            score = await self._calculate_basic_score(record)
        
        # 更新分数
        record.score = score
        record.reviewed_at = datetime.now()
        
        # 自动判定通过状态
        if score >= record.pass_threshold:
            record.pass_status = PassStatus.PASSED
            record.status = "approved"
        else:
            record.pass_status = PassStatus.FAILED
            record.status = "reviewed"
        
        await self.db.commit()
        await self.db.refresh(record)
        
        return GradeResponse.model_validate(record)

    async def get_student_grade_statistics(self, student_id: str, experiment_type: Optional[str] = None) -> Optional[GradeStatistics]:
        """获取学生成绩统计"""
        # 检查学生是否存在
        student_result = await self.db.execute(
            select(Student).where(Student.id == student_id)
        )
        student = student_result.scalar_one_or_none()
        if not student:
            return None
        
        # 构建查询条件
        query = select(ExperimentRecord).where(ExperimentRecord.student_id == student_id)
        if experiment_type:
            query = query.where(ExperimentRecord.experiment_type == experiment_type)
        
        result = await self.db.execute(query)
        records = result.scalars().all()
        
        if not records:
            return GradeStatistics(
                student_id=student_id,
                student_name=student.name,
                total_experiments=0
            )
        
        # 计算统计数据
        total_experiments = len(records)
        completed_experiments = len([r for r in records if r.status == "approved"])
        pending_experiments = len([r for r in records if r.pass_status == PassStatus.PENDING])
        passed_experiments = len([r for r in records if r.pass_status == PassStatus.PASSED])
        failed_experiments = len([r for r in records if r.pass_status == PassStatus.FAILED])
        reviewing_experiments = len([r for r in records if r.pass_status == PassStatus.REVIEWING])
        
        scores = [r.score for r in records if r.score is not None]
        average_score = sum(scores) / len(scores) if scores else None
        highest_score = max(scores) if scores else None
        lowest_score = min(scores) if scores else None
        
        pass_rate = (passed_experiments / total_experiments * 100) if total_experiments > 0 else 0.0
        
        latest_experiment_date = max([r.submit_time for r in records]) if records else None
        
        return GradeStatistics(
            student_id=student_id,
            student_name=student.name,
            total_experiments=total_experiments,
            completed_experiments=completed_experiments,
            pending_experiments=pending_experiments,
            passed_experiments=passed_experiments,
            failed_experiments=failed_experiments,
            reviewing_experiments=reviewing_experiments,
            average_score=average_score,
            highest_score=highest_score,
            lowest_score=lowest_score,
            pass_rate=pass_rate,
            latest_experiment_date=latest_experiment_date
        )

    async def get_class_grade_statistics(self, class_id: int, experiment_type: Optional[str] = None) -> Optional[ClassGradesSummary]:
        """获取班级成绩统计"""
        # 检查班级是否存在
        class_result = await self.db.execute(
            select(Class).where(Class.id == class_id)
        )
        class_info = class_result.scalar_one_or_none()
        if not class_info:
            return None
        
        # 获取班级所有学生
        students_result = await self.db.execute(
            select(Student).where(Student.class_id == class_id)
        )
        students = students_result.scalars().all()
        
        if not students:
            return ClassGradesSummary(
                class_id=class_id,
                class_name=class_info.class_name,
                class_code=class_info.class_code,
                total_students=0
            )
        
        # 获取所有实验记录
        student_ids = [s.id for s in students]
        query = select(ExperimentRecord).where(ExperimentRecord.student_id.in_(student_ids))
        if experiment_type:
            query = query.where(ExperimentRecord.experiment_type == experiment_type)
        
        records_result = await self.db.execute(query)
        records = records_result.scalars().all()
        
        # 计算班级统计
        total_students = len(students)
        students_with_experiments = len(set([r.student_id for r in records]))
        total_experiments = len(records)
        completed_experiments = len([r for r in records if r.status == "approved"])
        pending_experiments = len([r for r in records if r.pass_status == PassStatus.PENDING])
        passed_experiments = len([r for r in records if r.pass_status == PassStatus.PASSED])
        failed_experiments = len([r for r in records if r.pass_status == PassStatus.FAILED])
        reviewing_experiments = len([r for r in records if r.pass_status == PassStatus.REVIEWING])
        
        scores = [r.score for r in records if r.score is not None]
        class_average_score = sum(scores) / len(scores) if scores else None
        class_pass_rate = (passed_experiments / total_experiments * 100) if total_experiments > 0 else 0.0
        
        # 分数分布
        score_distribution = {
            "0-59": len([s for s in scores if s < 60]),
            "60-69": len([s for s in scores if 60 <= s < 70]),
            "70-79": len([s for s in scores if 70 <= s < 80]),
            "80-89": len([s for s in scores if 80 <= s < 90]),
            "90-100": len([s for s in scores if s >= 90])
        }
        
        # 获取每个学生的统计
        student_statistics = []
        for student in students:
            stats = await self.get_student_grade_statistics(student.id, experiment_type)
            if stats:
                student_statistics.append(stats)
        
        return ClassGradesSummary(
            class_id=class_id,
            class_name=class_info.class_name,
            class_code=class_info.class_code,
            total_students=total_students,
            students_with_experiments=students_with_experiments,
            total_experiments=total_experiments,
            completed_experiments=completed_experiments,
            pending_experiments=pending_experiments,
            passed_experiments=passed_experiments,
            failed_experiments=failed_experiments,
            reviewing_experiments=reviewing_experiments,
            class_average_score=class_average_score,
            class_pass_rate=class_pass_rate,
            score_distribution=score_distribution,
            student_statistics=student_statistics
        )

    async def batch_auto_grade(
        self,
        experiment_type: Optional[str] = None,
        class_id: Optional[int] = None,
        student_ids: Optional[List[str]] = None,
        use_ai_score: bool = True
    ) -> BatchGradeResult:
        """批量自动评分"""
        start_time = datetime.now()
        
        # 构建查询条件
        query = select(ExperimentRecord).where(ExperimentRecord.pass_status == PassStatus.PENDING)
        
        if experiment_type:
            query = query.where(ExperimentRecord.experiment_type == experiment_type)
        
        if class_id:
            # 获取班级学生ID
            students_result = await self.db.execute(
                select(Student.id).where(Student.class_id == class_id)
            )
            class_student_ids = [row[0] for row in students_result.fetchall()]
            query = query.where(ExperimentRecord.student_id.in_(class_student_ids))
        
        if student_ids:
            query = query.where(ExperimentRecord.student_id.in_(student_ids))
        
        result = await self.db.execute(query)
        records = result.scalars().all()
        
        processed_count = len(records)
        success_count = 0
        failed_count = 0
        failed_records = []
        
        for record in records:
            try:
                await self.auto_grade_experiment(record.id, use_ai_score)
                success_count += 1
            except Exception as e:
                failed_count += 1
                failed_records.append({
                    "record_id": record.id,
                    "student_id": record.student_id,
                    "error": str(e)
                })
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return BatchGradeResult(
            processed_count=processed_count,
            success_count=success_count,
            failed_count=failed_count,
            failed_records=failed_records,
            processing_time=processing_time
        )

    async def _calculate_basic_score(self, record: ExperimentRecord) -> float:
        """计算基础分数"""
        # 简单的评分逻辑，基于数据完整性和质量
        base_score = 60.0  # 基础分
        
        # 检查数据完整性
        if record.raw_data:
            base_score += 20.0
        
        if record.processed_data:
            base_score += 10.0
        
        if record.analysis_result:
            base_score += 10.0
        
        return min(base_score, 100.0)
