from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import asyncio

from app.models.experiment import AIAnalysis, Student, ExperimentRecord
from app.schemas.ai_analysis_schema import (
    AIAnalysisCreate, 
    AIAnalysisUpdate, 
    AIAnalysisRequest,
    AnalysisType,
    AnalysisStatus
)
from app.core.config import settings


class AIAnalysisService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_analysis(self, analysis_data: AIAnalysisCreate) -> AIAnalysis:
        """创建AI分析记录"""
        db_analysis = AIAnalysis(**analysis_data.model_dump())
        db_analysis.status = AnalysisStatus.PROCESSING
        
        self.db.add(db_analysis)
        await self.db.commit()
        await self.db.refresh(db_analysis)
        return db_analysis

    async def get_analysis(self, analysis_id: int) -> Optional[AIAnalysis]:
        """获取AI分析记录"""
        result = await self.db.execute(
            select(AIAnalysis).where(AIAnalysis.id == analysis_id)
        )
        return result.scalar_one_or_none()

    async def get_analyses(
        self,
        student_id: Optional[str] = None,
        experiment_type: Optional[str] = None,
        analysis_type: Optional[AnalysisType] = None,
        status: Optional[AnalysisStatus] = None,
        page: int = 1,
        size: int = 20
    ) -> List[AIAnalysis]:
        """获取AI分析记录列表"""
        query = select(AIAnalysis)
        
        conditions = []
        if student_id:
            conditions.append(AIAnalysis.student_id == student_id)
        if experiment_type:
            conditions.append(AIAnalysis.experiment_type == experiment_type)
        if analysis_type:
            conditions.append(AIAnalysis.analysis_type == analysis_type)
        if status:
            conditions.append(AIAnalysis.status == status)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.offset((page - 1) * size).limit(size)
        query = query.order_by(AIAnalysis.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()

    async def update_analysis(self, analysis_id: int, analysis_update: AIAnalysisUpdate) -> Optional[AIAnalysis]:
        """更新AI分析记录"""
        result = await self.db.execute(
            select(AIAnalysis).where(AIAnalysis.id == analysis_id)
        )
        db_analysis = result.scalar_one_or_none()
        
        if not db_analysis:
            return None
        
        update_data = analysis_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_analysis, field, value)
        
        await self.db.commit()
        await self.db.refresh(db_analysis)
        return db_analysis

    async def process_analysis_request(self, request: AIAnalysisRequest) -> AIAnalysis:
        """处理AI分析请求"""
        start_time = datetime.now()
        
        # 创建分析记录
        analysis_data = AIAnalysisCreate(
            student_id=request.student_id,
            experiment_type=request.experiment_type,
            analysis_type=request.analysis_type,
            analysis_data={},
            model_name=request.model_name or settings.DEFAULT_MODEL
        )
        
        db_analysis = await self.create_analysis(analysis_data)
        
        try:
            # 执行AI分析
            analysis_result = await self._perform_ai_analysis(
                request.experiment_data,
                request.experiment_type,
                request.analysis_type,
                request.model_name
            )
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新分析结果
            update_data = AIAnalysisUpdate(
                analysis_data=analysis_result["analysis_data"],
                conclusions=analysis_result.get("conclusions", []),
                suggestions=analysis_result.get("suggestions", []),
                error_analysis=analysis_result.get("error_analysis"),
                scoring_details=analysis_result.get("scoring_details"),
                confidence_score=analysis_result.get("confidence_score"),
                quality_metrics=analysis_result.get("quality_metrics"),
                status=AnalysisStatus.COMPLETED
            )
            
            db_analysis.processing_time = processing_time
            await self.update_analysis(db_analysis.id, update_data)
            
        except Exception as e:
            # 分析失败，更新错误信息
            await self.update_analysis(db_analysis.id, AIAnalysisUpdate(
                status=AnalysisStatus.FAILED,
                error_message=str(e)
            ))
            raise e
        
        return db_analysis

    async def _perform_ai_analysis(
        self,
        experiment_data: Dict[str, Any],
        experiment_type: str,
        analysis_type: AnalysisType,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """执行AI分析"""
        # 这里是AI分析的核心逻辑
        # 根据实验类型和数据进行分析
        
        if analysis_type == AnalysisType.AUTO:
            return await self._auto_analysis(experiment_data, experiment_type, model_name)
        elif analysis_type == AnalysisType.MANUAL:
            return await self._manual_analysis(experiment_data, experiment_type)
        else:  # HYBRID
            return await self._hybrid_analysis(experiment_data, experiment_type, model_name)

    async def _auto_analysis(
        self,
        experiment_data: Dict[str, Any],
        experiment_type: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """自动AI分析"""
        # 模拟AI分析过程
        await asyncio.sleep(1)  # 模拟处理时间
        
        # 基础数据分析
        analysis_result = {
            "analysis_data": {
                "data_quality": self._assess_data_quality(experiment_data),
                "statistical_analysis": self._perform_statistical_analysis(experiment_data),
                "pattern_recognition": self._recognize_patterns(experiment_data)
            },
            "conclusions": self._generate_conclusions(experiment_data, experiment_type),
            "suggestions": self._generate_suggestions(experiment_data, experiment_type),
            "scoring_details": self._calculate_scoring_details(experiment_data, experiment_type),
            "confidence_score": self._calculate_confidence_score(experiment_data),
            "quality_metrics": self._calculate_quality_metrics(experiment_data)
        }
        
        # 错误分析
        errors = self._detect_errors(experiment_data, experiment_type)
        if errors:
            analysis_result["error_analysis"] = errors
        
        return analysis_result

    async def _manual_analysis(
        self,
        experiment_data: Dict[str, Any],
        experiment_type: str
    ) -> Dict[str, Any]:
        """手动分析模式"""
        return {
            "analysis_data": {
                "manual_review_required": True,
                "data_summary": self._summarize_data(experiment_data)
            },
            "conclusions": ["需要人工审核"],
            "suggestions": ["请专业教师进行详细分析"],
            "confidence_score": 0.5
        }

    async def _hybrid_analysis(
        self,
        experiment_data: Dict[str, Any],
        experiment_type: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """混合分析模式"""
        # 先进行自动分析
        auto_result = await self._auto_analysis(experiment_data, experiment_type, model_name)
        
        # 添加人工审核标记
        auto_result["analysis_data"]["requires_human_review"] = True
        auto_result["analysis_data"]["auto_analysis_confidence"] = auto_result.get("confidence_score", 0.5)
        
        # 降低置信度，表示需要人工确认
        auto_result["confidence_score"] = min(auto_result.get("confidence_score", 0.5) * 0.8, 0.8)
        
        return auto_result

    def _assess_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """评估数据质量"""
        return {
            "completeness": 0.95,
            "accuracy": 0.90,
            "consistency": 0.88,
            "validity": 0.92
        }

    def _perform_statistical_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """执行统计分析"""
        return {
            "mean_values": {},
            "standard_deviations": {},
            "correlations": {},
            "outliers": []
        }

    def _recognize_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """识别数据模式"""
        return {
            "trends": [],
            "anomalies": [],
            "patterns": []
        }

    def _generate_conclusions(self, data: Dict[str, Any], experiment_type: str) -> List[str]:
        """生成结论"""
        return [
            "实验数据符合预期理论模型",
            "测量精度在可接受范围内",
            "实验方法正确"
        ]

    def _generate_suggestions(self, data: Dict[str, Any], experiment_type: str) -> List[str]:
        """生成改进建议"""
        return [
            "建议增加测量次数以提高精度",
            "注意环境因素对实验结果的影响",
            "可以尝试不同的实验参数"
        ]

    def _calculate_scoring_details(self, data: Dict[str, Any], experiment_type: str) -> Dict[str, Any]:
        """计算评分详情"""
        return {
            "data_accuracy": 85,
            "method_correctness": 90,
            "analysis_quality": 88,
            "total_score": 87.7
        }

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """计算置信度分数"""
        return 0.85

    def _calculate_quality_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """计算质量指标"""
        return {
            "precision": 0.92,
            "recall": 0.88,
            "f1_score": 0.90
        }

    def _detect_errors(self, data: Dict[str, Any], experiment_type: str) -> Optional[Dict[str, Any]]:
        """检测错误"""
        # 简单的错误检测逻辑
        errors = []
        
        # 检查数据完整性
        if not data or len(data) == 0:
            errors.append("数据为空")
        
        if errors:
            return {
                "error_count": len(errors),
                "error_list": errors,
                "severity": "medium"
            }
        
        return None

    def _summarize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """数据摘要"""
        return {
            "data_points": len(data) if isinstance(data, (list, dict)) else 0,
            "data_types": list(data.keys()) if isinstance(data, dict) else [],
            "summary": "实验数据摘要"
        }
