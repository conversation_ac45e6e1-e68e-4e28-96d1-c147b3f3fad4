-- 物理实验平台数据库初始化脚本
-- 创建数据库和用户已经在环境变量中设置

USE physics_experiments;

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建班级表
CREATE TABLE IF NOT EXISTS classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_code VARCHAR(50) UNIQUE NOT NULL COMMENT '班级代码',
    class_name VARCHAR(100) NOT NULL COMMENT '班级名称',
    grade VARCHAR(20) NOT NULL COMMENT '年级',
    major VARCHAR(100) NOT NULL COMMENT '专业',
    department VARCHAR(100) COMMENT '院系',
    teacher_name VARCHAR(100) COMMENT '任课教师',
    teacher_email VARCHAR(255) COMMENT '教师邮箱',
    academic_year VARCHAR(20) NOT NULL COMMENT '学年',
    semester VARCHAR(20) NOT NULL COMMENT '学期',
    student_count INT DEFAULT 0 COMMENT '学生人数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_class_code (class_code),
    INDEX idx_grade_major (grade, major),
    INDEX idx_academic_year (academic_year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='班级表';

-- 创建学生表
CREATE TABLE IF NOT EXISTS students (
    id VARCHAR(50) PRIMARY KEY COMMENT '学号',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    email VARCHAR(255) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    class_id INT COMMENT '班级ID',
    major VARCHAR(100) COMMENT '专业',
    grade VARCHAR(20) COMMENT '年级',
    enrollment_year VARCHAR(10) COMMENT '入学年份',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否在校',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_class_id (class_id),
    INDEX idx_grade (grade)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生表';

-- 创建实验模板表
CREATE TABLE IF NOT EXISTS experiment_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    experiment_id VARCHAR(100) UNIQUE NOT NULL COMMENT '实验ID',
    name VARCHAR(200) NOT NULL COMMENT '实验名称',
    description TEXT COMMENT '实验描述',
    category VARCHAR(100) NOT NULL COMMENT '实验分类',
    difficulty VARCHAR(20) DEFAULT 'medium' COMMENT '难度',
    duration INT DEFAULT 60 COMMENT '预计时长(分钟)',
    tags JSON COMMENT '标签列表',
    config JSON NOT NULL COMMENT '实验配置JSON',
    steps_config JSON NOT NULL COMMENT '步骤配置',
    scoring_config JSON COMMENT '评分配置',
    pass_criteria JSON COMMENT '通过标准',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    version VARCHAR(20) DEFAULT '1.0.0' COMMENT '版本号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_experiment_id (experiment_id),
    INDEX idx_category (category),
    INDEX idx_difficulty (difficulty)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实验模板表';

-- 创建AI分析表
CREATE TABLE IF NOT EXISTS ai_analyses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL COMMENT '学生ID',
    experiment_type VARCHAR(100) NOT NULL COMMENT '实验类型',
    analysis_type VARCHAR(50) DEFAULT 'auto' COMMENT '分析类型',
    analysis_data JSON NOT NULL COMMENT '详细分析数据',
    conclusions JSON COMMENT '结论列表',
    suggestions JSON COMMENT '改进建议',
    error_analysis JSON COMMENT '错误分析',
    scoring_details JSON COMMENT '评分详情',
    confidence_score FLOAT COMMENT '置信度分数',
    quality_metrics JSON COMMENT '质量指标',
    model_name VARCHAR(100) COMMENT '使用的AI模型',
    model_version VARCHAR(50) COMMENT '模型版本',
    processing_time FLOAT COMMENT '处理时间(秒)',
    status VARCHAR(50) DEFAULT 'completed' COMMENT '状态',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student_experiment (student_id, experiment_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI分析结果表';

-- 创建实验记录表
CREATE TABLE IF NOT EXISTS experiment_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL COMMENT '学生ID',
    experiment_type VARCHAR(100) NOT NULL COMMENT '实验类型',
    experiment_name VARCHAR(200) NOT NULL COMMENT '实验名称',
    version INT DEFAULT 1 COMMENT '版本号',
    status VARCHAR(50) DEFAULT 'submitted' COMMENT '状态',
    raw_data JSON COMMENT '原始数据',
    processed_data JSON COMMENT '处理后的数据',
    analysis_result JSON COMMENT '分析结果',
    charts_data JSON COMMENT '图表数据',
    score FLOAT COMMENT '分数',
    max_score FLOAT DEFAULT 100.0 COMMENT '满分',
    pass_threshold FLOAT DEFAULT 60.0 COMMENT '及格线',
    pass_status ENUM('pending', 'passed', 'failed', 'reviewing') DEFAULT 'pending' COMMENT '通过状态',
    feedback TEXT COMMENT '反馈',
    reviewer_id VARCHAR(50) COMMENT '评阅者ID',
    reviewed_at TIMESTAMP NULL COMMENT '评阅时间',
    ai_analysis_id INT COMMENT 'AI分析ID',
    auto_score FLOAT COMMENT 'AI自动评分',
    manual_override BOOLEAN DEFAULT FALSE COMMENT '是否人工覆盖',
    submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (ai_analysis_id) REFERENCES ai_analyses(id) ON DELETE SET NULL,
    INDEX idx_student_experiment (student_id, experiment_type),
    INDEX idx_status (status),
    INDEX idx_pass_status (pass_status),
    INDEX idx_submit_time (submit_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实验记录表';

-- 插入示例数据
INSERT INTO classes (class_code, class_name, grade, major, department, teacher_name, teacher_email, academic_year, semester) VALUES
('CS2021-1', '计算机科学与技术2021级1班', '2021', '计算机科学与技术', '计算机学院', '张教授', '<EMAIL>', '2023-2024', '第一学期'),
('CS2021-2', '计算机科学与技术2021级2班', '2021', '计算机科学与技术', '计算机学院', '李教授', '<EMAIL>', '2023-2024', '第一学期'),
('PHYS2022-1', '物理学2022级1班', '2022', '物理学', '物理学院', '王教授', '<EMAIL>', '2023-2024', '第一学期');

INSERT INTO students (id, name, email, class_id, major, grade, enrollment_year) VALUES
('2021001001', '张三', '<EMAIL>', 1, '计算机科学与技术', '2021', '2021'),
('2021001002', '李四', '<EMAIL>', 1, '计算机科学与技术', '2021', '2021'),
('2021001003', '王五', '<EMAIL>', 2, '计算机科学与技术', '2021', '2021'),
('2022002001', '赵六', '<EMAIL>', 3, '物理学', '2022', '2022'),
('2022002002', '钱七', '<EMAIL>', 3, '物理学', '2022', '2022');

INSERT INTO experiment_templates (experiment_id, name, description, category, difficulty, config, steps_config) VALUES
('pendulum', '单摆实验', '通过单摆实验测量重力加速度', '力学', 'easy', '{"equipment": ["单摆", "秒表", "量尺"], "duration": 60}', '{"steps": [{"name": "准备实验", "description": "准备实验器材"}, {"name": "测量摆长", "description": "使用量尺测量摆长"}, {"name": "测量周期", "description": "使用秒表测量摆动周期"}]}'),
('optics', '光学干涉实验', '观察光的干涉现象，测量光波长', '光学', 'medium', '{"equipment": ["激光器", "双缝", "屏幕"], "duration": 90}', '{"steps": [{"name": "调节光路", "description": "调节激光器和双缝位置"}, {"name": "观察干涉", "description": "在屏幕上观察干涉条纹"}, {"name": "测量数据", "description": "测量条纹间距"}]}');

-- 更新班级学生数量
UPDATE classes SET student_count = (
    SELECT COUNT(*) FROM students WHERE students.class_id = classes.id
);

-- 创建索引优化查询性能
CREATE INDEX idx_experiment_records_composite ON experiment_records(student_id, experiment_type, submit_time);
CREATE INDEX idx_ai_analyses_composite ON ai_analyses(student_id, experiment_type, created_at);

COMMIT;
