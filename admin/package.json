{"name": "physics-experiments-admin", "version": "1.0.0", "description": "物理实验平台后台管理系统", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "element-plus": "^2.4.2", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "sass": "^1.69.5", "typescript": "~5.2.0", "vite": "^5.0.0", "vue-tsc": "^1.8.22", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}