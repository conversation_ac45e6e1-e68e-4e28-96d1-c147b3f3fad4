from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from app.models.experiment import ExperimentRec<PERSON>, Student, ExperimentTemplate
from app.schemas.experiment import (
    ExperimentListItem,
    ExperimentConfig,
    ExperimentSubmission,
    ExperimentSubmissionResponse,
    ExperimentRecord as ExperimentRecordSchema,
    DifficultyLevel
)
from app.utils.data_processor import DataProcessor


class ExperimentService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.data_processor = DataProcessor()

    async def get_experiment_list(
        self, 
        category: Optional[str] = None,
        difficulty: Optional[str] = None
    ) -> List[ExperimentListItem]:
        """获取实验列表"""
        query = select(ExperimentTemplate).where(ExperimentTemplate.is_active == True)
        
        if category:
            query = query.where(ExperimentTemplate.category == category)
        
        if difficulty:
            query = query.where(ExperimentTemplate.difficulty == difficulty)
        
        result = await self.db.execute(query)
        templates = result.scalars().all()
        
        return [
            ExperimentListItem(
                id=template.experiment_id,
                name=template.name,
                description=template.description or "",
                category=template.category,
                difficulty=DifficultyLevel(template.difficulty),
                duration=template.duration,
                tags=template.tags or []
            )
            for template in templates
        ]

    async def get_experiment_config(self, experiment_id: str) -> Optional[ExperimentConfig]:
        """获取实验配置"""
        query = select(ExperimentTemplate).where(
            and_(
                ExperimentTemplate.experiment_id == experiment_id,
                ExperimentTemplate.is_active == True
            )
        )
        result = await self.db.execute(query)
        template = result.scalar_one_or_none()
        
        if not template:
            return None
        
        # 构建配置对象
        config_data = {
            "info": {
                "id": template.experiment_id,
                "name": template.name,
                "description": template.description or "",
                "category": template.category,
                "difficulty": template.difficulty,
                "duration": template.duration,
                "tags": template.tags or []
            },
            "steps": template.steps_config.get("steps", []),
            "theme": template.config.get("theme", {})
        }
        
        return ExperimentConfig(**config_data)

    async def submit_experiment(
        self, 
        experiment_id: str, 
        submission: ExperimentSubmission
    ) -> ExperimentSubmissionResponse:
        """提交实验数据"""
        try:
            # 验证学生信息
            student_id = submission.student_info.get("id")
            student_name = submission.student_info.get("name")
            
            if not student_id or not student_name:
                raise ValueError("学生信息不完整")
            
            # 确保学生记录存在
            await self._ensure_student_exists(student_id, student_name)
            
            # 获取实验模板信息
            template = await self._get_experiment_template(experiment_id)
            if not template:
                raise ValueError(f"实验模板不存在: {experiment_id}")
            
            # 处理实验数据
            processed_data = await self.data_processor.process_experiment_data(
                experiment_id, submission.step_data
            )
            
            # 检查是否已有提交记录
            existing_record = await self._get_latest_record(student_id, experiment_id)
            version = (existing_record.version + 1) if existing_record else 1
            
            # 创建实验记录
            record = ExperimentRecord(
                student_id=student_id,
                experiment_type=experiment_id,
                experiment_name=template.name,
                version=version,
                raw_data=submission.step_data,
                processed_data=processed_data,
                analysis_result=submission.analysis,
                charts_data=submission.charts,
                submit_time=datetime.fromisoformat(submission.submit_time) if submission.submit_time else datetime.now()
            )
            
            self.db.add(record)
            await self.db.commit()
            await self.db.refresh(record)
            
            # 更新模板统计信息
            await self._update_template_statistics(template.id)
            
            return ExperimentSubmissionResponse(
                success=True,
                message="实验数据提交成功",
                record_id=record.id,
                version=record.version
            )
            
        except Exception as e:
            await self.db.rollback()
            raise e

    async def get_experiment_records(
        self,
        experiment_id: Optional[str] = None,
        student_id: Optional[str] = None,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """获取实验记录列表"""
        query = select(ExperimentRecord)
        
        if experiment_id:
            query = query.where(ExperimentRecord.experiment_type == experiment_id)
        
        if student_id:
            query = query.where(ExperimentRecord.student_id == student_id)
        
        # 计算总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        query = query.order_by(ExperimentRecord.submit_time.desc())
        query = query.offset((page - 1) * size).limit(size)
        
        result = await self.db.execute(query)
        records = result.scalars().all()
        
        return {
            "items": [ExperimentRecordSchema.model_validate(record) for record in records],
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }

    async def get_experiment_record_by_id(self, record_id: int) -> Optional[ExperimentRecordSchema]:
        """根据ID获取实验记录"""
        query = select(ExperimentRecord).where(ExperimentRecord.id == record_id)
        result = await self.db.execute(query)
        record = result.scalar_one_or_none()
        
        if record:
            return ExperimentRecordSchema.model_validate(record)
        return None

    async def get_experiment_categories(self) -> List[str]:
        """获取实验分类列表"""
        query = select(ExperimentTemplate.category.distinct()).where(
            ExperimentTemplate.is_active == True
        )
        result = await self.db.execute(query)
        categories = result.scalars().all()
        return list(categories)

    async def get_experiment_statistics(self, experiment_id: Optional[str] = None) -> Dict[str, Any]:
        """获取实验统计信息"""
        stats = {}
        
        if experiment_id:
            # 特定实验的统计
            query = select(func.count()).where(ExperimentRecord.experiment_type == experiment_id)
            result = await self.db.execute(query)
            stats["total_submissions"] = result.scalar()
            
            # 平均分数
            query = select(func.avg(ExperimentRecord.score)).where(
                and_(
                    ExperimentRecord.experiment_type == experiment_id,
                    ExperimentRecord.score.isnot(None)
                )
            )
            result = await self.db.execute(query)
            avg_score = result.scalar()
            stats["average_score"] = float(avg_score) if avg_score else None
            
        else:
            # 全局统计
            query = select(func.count()).select_from(ExperimentRecord)
            result = await self.db.execute(query)
            stats["total_submissions"] = result.scalar()
            
            query = select(func.count().distinct(ExperimentRecord.student_id))
            result = await self.db.execute(query)
            stats["unique_students"] = result.scalar()
            
            query = select(func.count()).select_from(ExperimentTemplate).where(
                ExperimentTemplate.is_active == True
            )
            result = await self.db.execute(query)
            stats["active_experiments"] = result.scalar()
        
        return stats

    async def _ensure_student_exists(self, student_id: str, student_name: str):
        """确保学生记录存在"""
        query = select(Student).where(Student.id == student_id)
        result = await self.db.execute(query)
        student = result.scalar_one_or_none()
        
        if not student:
            student = Student(id=student_id, name=student_name)
            self.db.add(student)
            await self.db.commit()
        elif student.name != student_name:
            student.name = student_name
            await self.db.commit()

    async def _get_experiment_template(self, experiment_id: str) -> Optional[ExperimentTemplate]:
        """获取实验模板"""
        query = select(ExperimentTemplate).where(
            and_(
                ExperimentTemplate.experiment_id == experiment_id,
                ExperimentTemplate.is_active == True
            )
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def _get_latest_record(self, student_id: str, experiment_id: str) -> Optional[ExperimentRecord]:
        """获取学生最新的实验记录"""
        query = select(ExperimentRecord).where(
            and_(
                ExperimentRecord.student_id == student_id,
                ExperimentRecord.experiment_type == experiment_id
            )
        ).order_by(ExperimentRecord.version.desc())
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def _update_template_statistics(self, template_id: int):
        """更新模板统计信息"""
        # 更新提交总数
        query = select(func.count()).where(
            ExperimentRecord.experiment_type == select(ExperimentTemplate.experiment_id).where(
                ExperimentTemplate.id == template_id
            ).scalar_subquery()
        )
        result = await self.db.execute(query)
        total_submissions = result.scalar()
        
        # 更新平均分数
        query = select(func.avg(ExperimentRecord.score)).where(
            and_(
                ExperimentRecord.experiment_type == select(ExperimentTemplate.experiment_id).where(
                    ExperimentTemplate.id == template_id
                ).scalar_subquery(),
                ExperimentRecord.score.isnot(None)
            )
        )
        result = await self.db.execute(query)
        avg_score = result.scalar()
        
        # 更新模板
        query = select(ExperimentTemplate).where(ExperimentTemplate.id == template_id)
        result = await self.db.execute(query)
        template = result.scalar_one_or_none()
        
        if template:
            template.total_submissions = total_submissions
            template.average_score = float(avg_score) if avg_score else None
            await self.db.commit()
