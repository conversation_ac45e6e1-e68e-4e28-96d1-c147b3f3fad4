import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      hidden: true
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'DataAnalysis'
        }
      }
    ]
  },
  {
    path: '/students',
    component: Layout,
    meta: {
      title: '学生管理',
      icon: 'User'
    },
    children: [
      {
        path: '',
        name: 'StudentList',
        component: () => import('@/views/students/index.vue'),
        meta: {
          title: '学生列表',
          icon: 'User'
        }
      },
      {
        path: ':id',
        name: 'StudentDetail',
        component: () => import('@/views/students/detail.vue'),
        meta: {
          title: '学生详情',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/classes',
    component: Layout,
    meta: {
      title: '班级管理',
      icon: 'School'
    },
    children: [
      {
        path: '',
        name: 'ClassList',
        component: () => import('@/views/classes/index.vue'),
        meta: {
          title: '班级列表',
          icon: 'School'
        }
      },
      {
        path: ':id',
        name: 'ClassDetail',
        component: () => import('@/views/classes/detail.vue'),
        meta: {
          title: '班级详情',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/experiments',
    component: Layout,
    meta: {
      title: '实验管理',
      icon: 'Experiment'
    },
    children: [
      {
        path: '',
        name: 'ExperimentList',
        component: () => import('@/views/experiments/index.vue'),
        meta: {
          title: '实验列表',
          icon: 'Experiment'
        }
      },
      {
        path: 'records',
        name: 'ExperimentRecords',
        component: () => import('@/views/experiments/records.vue'),
        meta: {
          title: '实验记录',
          icon: 'Document'
        }
      },
      {
        path: 'records/:id',
        name: 'ExperimentRecordDetail',
        component: () => import('@/views/experiments/record-detail.vue'),
        meta: {
          title: '实验记录详情',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/grades',
    component: Layout,
    meta: {
      title: '成绩管理',
      icon: 'Trophy'
    },
    children: [
      {
        path: '',
        name: 'GradeList',
        component: () => import('@/views/grades/index.vue'),
        meta: {
          title: '成绩管理',
          icon: 'Trophy'
        }
      },
      {
        path: 'statistics',
        name: 'GradeStatistics',
        component: () => import('@/views/grades/statistics.vue'),
        meta: {
          title: '成绩统计',
          icon: 'DataAnalysis'
        }
      }
    ]
  },
  {
    path: '/ai-analysis',
    component: Layout,
    meta: {
      title: 'AI分析',
      icon: 'MagicStick'
    },
    children: [
      {
        path: '',
        name: 'AIAnalysisList',
        component: () => import('@/views/ai-analysis/index.vue'),
        meta: {
          title: 'AI分析记录',
          icon: 'MagicStick'
        }
      },
      {
        path: ':id',
        name: 'AIAnalysisDetail',
        component: () => import('@/views/ai-analysis/detail.vue'),
        meta: {
          title: 'AI分析详情',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    meta: {
      title: '系统管理',
      icon: 'Setting'
    },
    children: [
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/system/settings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting'
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
