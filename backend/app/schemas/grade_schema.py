from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

from app.models.experiment import PassStatus


class GradeUpdate(BaseModel):
    """成绩更新Schema"""
    score: Optional[float] = Field(None, ge=0, le=100, description="分数")
    max_score: Optional[float] = Field(None, ge=0, description="满分")
    pass_threshold: Optional[float] = Field(None, ge=0, description="及格线")
    feedback: Optional[str] = Field(None, description="反馈意见")
    reviewer_id: Optional[str] = Field(None, description="评阅者ID")
    manual_override: Optional[bool] = Field(None, description="是否人工覆盖")


class PassStatusUpdate(BaseModel):
    """通过状态更新Schema"""
    pass_status: PassStatus = Field(..., description="通过状态")
    feedback: Optional[str] = Field(None, description="反馈意见")
    reviewer_id: Optional[str] = Field(None, description="评阅者ID")


class GradeResponse(BaseModel):
    """成绩响应Schema"""
    record_id: int
    student_id: str
    experiment_type: str
    score: Optional[float] = None
    max_score: float
    pass_threshold: float
    pass_status: PassStatus
    feedback: Optional[str] = None
    reviewer_id: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    auto_score: Optional[float] = None
    manual_override: bool = False

    class Config:
        from_attributes = True


class GradeStatistics(BaseModel):
    """成绩统计Schema"""
    student_id: str
    student_name: str
    total_experiments: int = 0
    completed_experiments: int = 0
    pending_experiments: int = 0
    passed_experiments: int = 0
    failed_experiments: int = 0
    reviewing_experiments: int = 0
    average_score: Optional[float] = None
    highest_score: Optional[float] = None
    lowest_score: Optional[float] = None
    pass_rate: float = 0.0
    latest_experiment_date: Optional[datetime] = None
    experiment_details: List[Dict[str, Any]] = []


class ClassGradesSummary(BaseModel):
    """班级成绩汇总Schema"""
    class_id: int
    class_name: str
    class_code: str
    total_students: int = 0
    students_with_experiments: int = 0
    total_experiments: int = 0
    completed_experiments: int = 0
    pending_experiments: int = 0
    passed_experiments: int = 0
    failed_experiments: int = 0
    reviewing_experiments: int = 0
    class_average_score: Optional[float] = None
    class_pass_rate: float = 0.0
    score_distribution: Dict[str, int] = {}
    student_statistics: List[GradeStatistics] = []


class ExperimentGradeStatistics(BaseModel):
    """实验成绩统计Schema"""
    experiment_type: str
    experiment_name: str
    total_submissions: int = 0
    completed_submissions: int = 0
    pending_submissions: int = 0
    passed_submissions: int = 0
    failed_submissions: int = 0
    reviewing_submissions: int = 0
    average_score: Optional[float] = None
    median_score: Optional[float] = None
    highest_score: Optional[float] = None
    lowest_score: Optional[float] = None
    pass_rate: float = 0.0
    score_distribution: Dict[str, int] = {}
    difficulty_analysis: Dict[str, Any] = {}


class BatchGradeResult(BaseModel):
    """批量评分结果Schema"""
    processed_count: int = 0
    success_count: int = 0
    failed_count: int = 0
    failed_records: List[Dict[str, Any]] = []
    processing_time: float = 0.0


class PendingReviewRecord(BaseModel):
    """待审核记录Schema"""
    record_id: int
    student_id: str
    student_name: str
    class_id: Optional[int] = None
    class_name: Optional[str] = None
    experiment_type: str
    experiment_name: str
    submit_time: datetime
    auto_score: Optional[float] = None
    ai_confidence: Optional[float] = None
    requires_review_reason: Optional[str] = None

    class Config:
        from_attributes = True


class GradeExportData(BaseModel):
    """成绩导出数据Schema"""
    file_url: str
    file_name: str
    export_time: datetime
    record_count: int
    format: str


class PassThreshold(BaseModel):
    """通过阈值Schema"""
    experiment_type: str
    default_threshold: float = 60.0
    excellent_threshold: float = 90.0
    good_threshold: float = 80.0
    fair_threshold: float = 70.0
    auto_pass_threshold: float = 85.0  # AI自动判定通过的阈值
    manual_review_threshold: float = 55.0  # 需要人工审核的阈值


class ScoreDistribution(BaseModel):
    """分数分布Schema"""
    range_0_59: int = 0    # 不及格
    range_60_69: int = 0   # 及格
    range_70_79: int = 0   # 中等
    range_80_89: int = 0   # 良好
    range_90_100: int = 0  # 优秀


class GradeTrend(BaseModel):
    """成绩趋势Schema"""
    date: datetime
    average_score: float
    pass_rate: float
    submission_count: int


class StudentGradeDetail(BaseModel):
    """学生成绩详情Schema"""
    record_id: int
    experiment_type: str
    experiment_name: str
    version: int
    score: Optional[float] = None
    max_score: float = 100.0
    pass_threshold: float = 60.0
    pass_status: PassStatus
    submit_time: datetime
    reviewed_at: Optional[datetime] = None
    feedback: Optional[str] = None
    auto_score: Optional[float] = None
    manual_override: bool = False
    ai_analysis_id: Optional[int] = None

    class Config:
        from_attributes = True


class GradeComparison(BaseModel):
    """成绩对比Schema"""
    student_id: str
    student_name: str
    current_score: Optional[float] = None
    class_average: Optional[float] = None
    rank_in_class: Optional[int] = None
    percentile: Optional[float] = None
    improvement_suggestions: List[str] = []
