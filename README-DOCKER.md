# 物理实验平台 - Docker容器化部署

## 🏗️ 架构概述

本项目采用微服务架构，将系统拆分为以下独立的Docker容器：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   管理后台      │    │   Nginx代理     │
│  (Vue.js)       │    │  (Vue.js)       │    │                 │
│  Port: 3000     │    │  Port: 8080     │    │  Port: 80/443   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端API       │
                    │  (FastAPI)      │
                    │  Port: 8000     │
                    └─────────────────┘
                             │
                ┌────────────┼────────────┐
                │                         │
    ┌─────────────────┐        ┌─────────────────┐
    │   MySQL数据库   │        │   Redis缓存     │
    │  Port: 3306     │        │  Port: 6379     │
    └─────────────────┘        └─────────────────┘
```

## 📦 容器服务

### 1. 数据库容器 (physics-database)
- **镜像**: 自定义MySQL 8.0
- **功能**: 存储所有业务数据
- **端口**: 3306
- **数据卷**: mysql_data, mysql_logs

### 2. 缓存容器 (physics-redis)
- **镜像**: Redis 7-alpine
- **功能**: 缓存和会话存储
- **端口**: 6379
- **数据卷**: redis_data

### 3. 后端API容器 (physics-api)
- **镜像**: 自定义Python FastAPI
- **功能**: 提供REST API服务
- **端口**: 8000
- **数据卷**: backend_static, backend_logs

### 4. 前端应用容器 (physics-frontend)
- **镜像**: 自定义Nginx + Vue.js
- **功能**: 学生实验界面
- **端口**: 3000

### 5. 管理后台容器 (physics-admin)
- **镜像**: 自定义Nginx + Vue.js
- **功能**: 教师管理界面
- **端口**: 8080

### 6. 反向代理容器 (physics-nginx)
- **镜像**: Nginx Alpine
- **功能**: 负载均衡和SSL终止
- **端口**: 80, 443

## 🚀 快速启动

### 前置要求
- Docker >= 20.10
- Docker Compose >= 2.0
- 至少4GB可用内存
- 至少10GB可用磁盘空间

### 启动步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd physics-experiments-refactor
```

2. **设置权限**
```bash
chmod +x scripts/start-all.sh
chmod +x scripts/stop-all.sh
```

3. **启动所有服务**
```bash
./scripts/start-all.sh
```

4. **访问应用**
- 前端应用: http://localhost:3000
- 管理后台: http://localhost:8080
- API文档: http://localhost:8000/docs
- API Redoc: http://localhost:8000/redoc

## 🔧 配置说明

### 环境变量

#### 数据库配置
```env
MYSQL_ROOT_PASSWORD=physics_root_2024
MYSQL_DATABASE=physics_experiments
MYSQL_USER=physics_user
MYSQL_PASSWORD=physics_pass_2024
```

#### 后端API配置
```env
DATABASE_URL=mysql+aiomysql://physics_user:physics_pass_2024@database:3306/physics_experiments
REDIS_URL=redis://redis:6379/0
PROJECT_NAME=Physics Experiments Platform
API_V1_STR=/api/v1
SECRET_KEY=physics_secret_key_2024_change_in_production
LOG_LEVEL=INFO
```

### 数据卷

- `mysql_data`: MySQL数据文件
- `mysql_logs`: MySQL日志文件
- `redis_data`: Redis数据文件
- `backend_static`: 后端静态文件
- `backend_logs`: 后端日志文件

## 📊 数据库设计

### 核心表结构

#### 班级表 (classes)
```sql
CREATE TABLE classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_code VARCHAR(50) UNIQUE NOT NULL,
    class_name VARCHAR(100) NOT NULL,
    grade VARCHAR(20) NOT NULL,
    major VARCHAR(100) NOT NULL,
    teacher_name VARCHAR(100),
    academic_year VARCHAR(20) NOT NULL,
    semester VARCHAR(20) NOT NULL,
    student_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 学生表 (students)
```sql
CREATE TABLE students (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    class_id INT,
    major VARCHAR(100),
    grade VARCHAR(20),
    enrollment_year VARCHAR(10),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id)
);
```

#### 实验记录表 (experiment_records)
```sql
CREATE TABLE experiment_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    experiment_type VARCHAR(100) NOT NULL,
    experiment_name VARCHAR(200) NOT NULL,
    version INT DEFAULT 1,
    status VARCHAR(50) DEFAULT 'submitted',
    raw_data JSON,
    processed_data JSON,
    analysis_result JSON,
    score FLOAT,
    max_score FLOAT DEFAULT 100.0,
    pass_threshold FLOAT DEFAULT 60.0,
    pass_status ENUM('pending', 'passed', 'failed', 'reviewing') DEFAULT 'pending',
    feedback TEXT,
    ai_analysis_id INT,
    auto_score FLOAT,
    manual_override BOOLEAN DEFAULT FALSE,
    submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (ai_analysis_id) REFERENCES ai_analyses(id)
);
```

#### AI分析表 (ai_analyses)
```sql
CREATE TABLE ai_analyses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    experiment_type VARCHAR(100) NOT NULL,
    analysis_type VARCHAR(50) DEFAULT 'auto',
    analysis_data JSON NOT NULL,
    conclusions JSON,
    suggestions JSON,
    confidence_score FLOAT,
    model_name VARCHAR(100),
    processing_time FLOAT,
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id)
);
```

## 🛠️ 管理命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f database
```

### 进入容器
```bash
# 进入数据库容器
docker-compose exec database mysql -u root -p

# 进入后端容器
docker-compose exec backend bash

# 进入Redis容器
docker-compose exec redis redis-cli
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 停止服务
```bash
# 停止所有服务
./scripts/stop-all.sh

# 或者使用docker-compose
docker-compose down
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口是否被占用: `netstat -tulpn | grep :3306`
   - 修改docker-compose.yml中的端口映射

2. **数据库连接失败**
   - 检查数据库容器状态: `docker-compose ps database`
   - 查看数据库日志: `docker-compose logs database`

3. **内存不足**
   - 检查系统内存: `free -h`
   - 调整MySQL配置中的buffer_pool_size

4. **磁盘空间不足**
   - 检查磁盘空间: `df -h`
   - 清理Docker资源: `docker system prune -f`

### 健康检查

所有容器都配置了健康检查，可以通过以下命令查看：

```bash
docker-compose ps
```

健康状态说明：
- `healthy`: 服务正常运行
- `unhealthy`: 服务异常
- `starting`: 服务启动中

## 📈 监控和日志

### 日志位置
- 后端日志: `backend_logs` 数据卷
- MySQL日志: `mysql_logs` 数据卷
- Nginx日志: 容器内 `/var/log/nginx/`

### 性能监控
建议使用以下工具进行监控：
- Prometheus + Grafana
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Docker Stats: `docker stats`

## 🔒 安全配置

### 生产环境建议
1. 修改默认密码
2. 启用SSL/TLS
3. 配置防火墙规则
4. 定期更新镜像
5. 使用非root用户运行容器

### 备份策略
```bash
# 数据库备份
docker-compose exec database mysqldump -u root -p physics_experiments > backup.sql

# 数据卷备份
docker run --rm -v mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_backup.tar.gz /data
```

## 📞 支持

如有问题，请查看：
1. 项目文档
2. Docker日志
3. GitHub Issues
4. 联系开发团队
