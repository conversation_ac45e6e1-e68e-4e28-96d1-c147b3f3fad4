from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from app.core.database import get_db
from app.schemas.class_schema import (
    Class,
    ClassCreate,
    ClassUpdate,
    ClassWithStudents,
    ClassSimple
)
from app.schemas.student_schema import StudentStats
from app.services.class_service import ClassService

router = APIRouter()


@router.post("/", response_model=Class)
async def create_class(
    class_data: ClassCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建班级"""
    class_service = ClassService(db)
    
    # 检查班级代码是否已存在
    existing_class = await class_service.get_class_by_code(class_data.class_code)
    if existing_class:
        raise HTTPException(status_code=400, detail="班级代码已存在")
    
    return await class_service.create_class(class_data)


@router.get("/", response_model=List[Class])
async def get_classes(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    grade: Optional[str] = Query(None, description="年级筛选"),
    major: Optional[str] = Query(None, description="专业筛选"),
    academic_year: Optional[str] = Query(None, description="学年筛选"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取班级列表"""
    class_service = ClassService(db)
    return await class_service.get_classes(
        page=page,
        size=size,
        search=search,
        grade=grade,
        major=major,
        academic_year=academic_year,
        is_active=is_active
    )


@router.get("/{class_id}", response_model=ClassWithStudents)
async def get_class(
    class_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取班级详情"""
    class_service = ClassService(db)
    class_info = await class_service.get_class_with_students(class_id)
    if not class_info:
        raise HTTPException(status_code=404, detail="班级不存在")
    return class_info


@router.put("/{class_id}", response_model=Class)
async def update_class(
    class_id: int,
    class_update: ClassUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新班级信息"""
    class_service = ClassService(db)
    class_info = await class_service.update_class(class_id, class_update)
    if not class_info:
        raise HTTPException(status_code=404, detail="班级不存在")
    return class_info


@router.delete("/{class_id}")
async def delete_class(
    class_id: int,
    force: bool = Query(False, description="是否强制删除（即使有学生）"),
    db: AsyncSession = Depends(get_db)
):
    """删除班级"""
    class_service = ClassService(db)
    
    # 检查班级是否存在
    class_info = await class_service.get_class(class_id)
    if not class_info:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 检查是否有学生
    if class_info.student_count > 0 and not force:
        raise HTTPException(
            status_code=400, 
            detail=f"班级中还有 {class_info.student_count} 名学生，无法删除。使用 force=true 强制删除。"
        )
    
    success = await class_service.delete_class(class_id, force)
    if not success:
        raise HTTPException(status_code=500, detail="删除失败")
    
    return {"message": "班级删除成功"}


@router.get("/{class_id}/students/stats")
async def get_class_student_stats(
    class_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取班级学生统计信息"""
    class_service = ClassService(db)
    
    # 检查班级是否存在
    class_info = await class_service.get_class(class_id)
    if not class_info:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    stats = await class_service.get_class_student_stats(class_id)
    return {
        "class_info": class_info,
        "student_stats": stats
    }


@router.post("/{class_id}/students/{student_id}")
async def add_student_to_class(
    class_id: int,
    student_id: str,
    db: AsyncSession = Depends(get_db)
):
    """将学生添加到班级"""
    class_service = ClassService(db)
    
    success = await class_service.add_student_to_class(class_id, student_id)
    if not success:
        raise HTTPException(status_code=400, detail="添加学生到班级失败")
    
    return {"message": "学生添加到班级成功"}


@router.delete("/{class_id}/students/{student_id}")
async def remove_student_from_class(
    class_id: int,
    student_id: str,
    db: AsyncSession = Depends(get_db)
):
    """从班级中移除学生"""
    class_service = ClassService(db)
    
    success = await class_service.remove_student_from_class(class_id, student_id)
    if not success:
        raise HTTPException(status_code=400, detail="从班级中移除学生失败")
    
    return {"message": "学生从班级中移除成功"}


@router.get("/codes/available")
async def check_class_code_available(
    class_code: str = Query(..., description="班级代码"),
    db: AsyncSession = Depends(get_db)
):
    """检查班级代码是否可用"""
    class_service = ClassService(db)
    existing_class = await class_service.get_class_by_code(class_code)
    
    return {
        "class_code": class_code,
        "available": existing_class is None
    }
